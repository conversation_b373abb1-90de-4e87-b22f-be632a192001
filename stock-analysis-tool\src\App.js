import React, { useState, useEffect } from 'react';
import { Search, ArrowUp, ArrowDown, TrendingUp, Brain, BarChart3, Wifi, WifiOff } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';

// 简化的股票分析工具
const StockAnalysisTool = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStock, setSelectedStock] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [predictions, setPredictions] = useState(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // 模拟股票数据
  const mockStocks = {
    'AAPL': {
      symbol: 'AAPL',
      name: 'Apple Inc.',
      price: 185.75,
      change: 2.35,
      changePercent: 1.28,
      volume: 45680000,
      marketCap: 2890000000000,
      pe: 28.5,
      high52: 198.23,
      low52: 124.17,
      sector: 'Technology',
      industry: 'Consumer Electronics'
    },
    'TSLA': {
      symbol: 'TSLA',
      name: 'Tesla, Inc.',
      price: 248.50,
      change: -8.45,
      changePercent: -3.29,
      volume: 98750000,
      marketCap: 789000000000,
      pe: 65.2,
      high52: 299.29,
      low52: 138.80,
      sector: 'Consumer Cyclical',
      industry: 'Auto Manufacturers'
    },
    'MSFT': {
      symbol: 'MSFT',
      name: 'Microsoft Corporation',
      price: 378.85,
      change: 5.20,
      changePercent: 1.39,
      volume: 28450000,
      marketCap: 2810000000000,
      pe: 32.1,
      high52: 384.30,
      low52: 309.45,
      sector: 'Technology',
      industry: 'Software'
    }
  };

  // 生成历史数据
  const generateHistoricalData = () => {
    const data = [];
    let price = selectedStock?.price || 150;
    for (let i = 30; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      price += (Math.random() - 0.5) * 10;
      data.push({
        date: date.toISOString().split('T')[0],
        price: parseFloat(price.toFixed(2)),
        volume: Math.floor(Math.random() * 100000000) + 20000000
      });
    }
    return data;
  };

  // 搜索股票
  const searchStock = (term) => {
    const upperTerm = term.toUpperCase();
    const found = mockStocks[upperTerm];
    
    if (found) {
      setSelectedStock(found);
    } else {
      alert(`未找到股票: ${term}\n\n可搜索的股票: AAPL, TSLA, MSFT`);
    }
  };

  // LSTM预测
  const runPrediction = async () => {
    if (!selectedStock) {
      alert('请先选择一只股票');
      return;
    }
    
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const currentPrice = selectedStock.price;
    const predictions = [];
    let predictedPrice = currentPrice;
    
    for (let i = 1; i <= 7; i++) {
      predictedPrice += (Math.random() - 0.45) * 5;
      predictions.push({
        day: i,
        predictedPrice: parseFloat(predictedPrice.toFixed(2)),
        confidence: Math.max(0.6, 0.95 - i * 0.05)
      });
    }
    
    setPredictions(predictions);
    setIsLoading(false);
  };

  const handleSearch = () => {
    if (searchTerm.trim()) {
      searchStock(searchTerm.trim());
    }
  };

  const StockCard = ({ stock }) => (
    <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-200 mb-6">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">{stock.name}</h2>
          <p className="text-gray-600">{stock.symbol}</p>
        </div>
        <div className="text-right">
          <div className="text-3xl font-bold text-gray-900">${stock.price}</div>
          <div className={`flex items-center ${stock.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {stock.changePercent >= 0 ? <ArrowUp className="w-4 h-4 mr-1" /> : <ArrowDown className="w-4 h-4 mr-1" />}
            ${Math.abs(stock.change)} ({Math.abs(stock.changePercent)}%)
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div>
          <div className="text-gray-500">市值</div>
          <div className="font-semibold">${(stock.marketCap / 1e9).toFixed(1)}B</div>
        </div>
        <div>
          <div className="text-gray-500">P/E比率</div>
          <div className="font-semibold">{stock.pe}</div>
        </div>
        <div>
          <div className="text-gray-500">成交量</div>
          <div className="font-semibold">{(stock.volume / 1e6).toFixed(1)}M</div>
        </div>
        <div>
          <div className="text-gray-500">行业</div>
          <div className="font-semibold">{stock.industry}</div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center items-center mb-4">
            <h1 className="text-4xl font-bold text-gray-900 mr-4">
              股票分析工具
            </h1>
            <div className="flex items-center">
              {isOnline ? <Wifi className="w-5 h-5 text-green-600" /> : <WifiOff className="w-5 h-5 text-red-600" />}
              <span className={`text-sm ml-1 ${isOnline ? 'text-green-600' : 'text-red-600'}`}>
                {isOnline ? '在线' : '离线'}
              </span>
            </div>
          </div>
          <p className="text-gray-600">基于LSTM深度学习的智能股票分析与预测系统</p>
        </div>

        {/* Search */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <div className="flex gap-4">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              placeholder="输入股票代码 (如: AAPL, TSLA, MSFT)"
              className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <button
              onClick={handleSearch}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
            >
              <Search className="w-5 h-5 mr-2" />
              搜索
            </button>
          </div>
          
          <div className="mt-4 flex flex-wrap gap-2">
            <span className="text-sm text-gray-600">快捷选择:</span>
            {Object.keys(mockStocks).map(symbol => (
              <button
                key={symbol}
                onClick={() => {
                  setSearchTerm(symbol);
                  searchStock(symbol);
                }}
                className="bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm transition-colors"
              >
                {symbol}
              </button>
            ))}
          </div>
        </div>

        {/* Stock Info */}
        {selectedStock && (
          <>
            <StockCard stock={selectedStock} />
            
            {/* Charts and Analysis */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
              {/* Price Chart */}
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <h3 className="text-xl font-bold mb-4 flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2 text-blue-600" />
                  价格走势
                </h3>
                <ResponsiveContainer width="100%" height={250}>
                  <LineChart data={generateHistoricalData()}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" tick={{ fontSize: 12 }} />
                    <YAxis tick={{ fontSize: 12 }} />
                    <Tooltip />
                    <Line type="monotone" dataKey="price" stroke="#3B82F6" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </div>

              {/* Volume Chart */}
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <h3 className="text-xl font-bold mb-4 flex items-center">
                  <BarChart3 className="w-5 h-5 mr-2 text-green-600" />
                  成交量
                </h3>
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart data={generateHistoricalData().slice(-7)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" tick={{ fontSize: 12 }} />
                    <YAxis tick={{ fontSize: 12 }} />
                    <Tooltip />
                    <Bar dataKey="volume" fill="#10B981" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* AI Prediction */}
            <div className="bg-white rounded-xl p-6 shadow-lg">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-xl font-bold flex items-center">
                  <Brain className="w-5 h-5 mr-2 text-purple-600" />
                  LSTM预测分析
                </h3>
                <button
                  onClick={runPrediction}
                  disabled={isLoading}
                  className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:opacity-50"
                >
                  {isLoading ? '分析中...' : '运行预测'}
                </button>
              </div>
              
              {isLoading && (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">正在运行LSTM神经网络预测...</p>
                </div>
              )}
              
              {predictions && !isLoading && (
                <div>
                  <ResponsiveContainer width="100%" height={200}>
                    <LineChart data={predictions}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis />
                      <Tooltip />
                      <Line type="monotone" dataKey="predictedPrice" stroke="#8B5CF6" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                  <div className="mt-4 grid grid-cols-3 md:grid-cols-7 gap-2">
                    {predictions.map((pred, idx) => (
                      <div key={idx} className="p-2 bg-purple-50 rounded text-center">
                        <div className="text-xs text-gray-600">第{pred.day}天</div>
                        <div className="font-bold text-sm">${pred.predictedPrice}</div>
                        <div className="text-xs text-green-600">{(pred.confidence * 100).toFixed(0)}%</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </>
        )}
        
        {!selectedStock && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📈</div>
            <h3 className="text-xl font-semibold text-gray-600 mb-2">开始分析股票</h3>
            <p className="text-gray-500">在上方搜索框中输入股票代码开始分析</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default StockAnalysisTool;
