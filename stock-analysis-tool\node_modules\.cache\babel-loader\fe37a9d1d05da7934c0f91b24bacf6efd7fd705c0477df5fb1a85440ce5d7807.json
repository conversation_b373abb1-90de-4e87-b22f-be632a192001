{"ast": null, "code": "import { timeInterval } from \"./interval.js\";\nexport const timeMonth = timeInterval(date => {\n  date.setDate(1);\n  date.setHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setMonth(date.getMonth() + step);\n}, (start, end) => {\n  return end.getMonth() - start.getMonth() + (end.getFullYear() - start.getFullYear()) * 12;\n}, date => {\n  return date.getMonth();\n});\nexport const timeMonths = timeMonth.range;\nexport const utcMonth = timeInterval(date => {\n  date.setUTCDate(1);\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCMonth(date.getUTCMonth() + step);\n}, (start, end) => {\n  return end.getUTCMonth() - start.getUTCMonth() + (end.getUTCFullYear() - start.getUTCFullYear()) * 12;\n}, date => {\n  return date.getUTCMonth();\n});\nexport const utcMonths = utcMonth.range;", "map": {"version": 3, "names": ["timeInterval", "timeMonth", "date", "setDate", "setHours", "step", "setMonth", "getMonth", "start", "end", "getFullYear", "timeMonths", "range", "utcMonth", "setUTCDate", "setUTCHours", "setUTCMonth", "getUTCMonth", "getUTCFullYear", "utcMonths"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/d3-time/src/month.js"], "sourcesContent": ["import {timeInterval} from \"./interval.js\";\n\nexport const timeMonth = timeInterval((date) => {\n  date.setDate(1);\n  date.setHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setMonth(date.getMonth() + step);\n}, (start, end) => {\n  return end.getMonth() - start.getMonth() + (end.getFullYear() - start.getFullYear()) * 12;\n}, (date) => {\n  return date.getMonth();\n});\n\nexport const timeMonths = timeMonth.range;\n\nexport const utcMonth = timeInterval((date) => {\n  date.setUTCDate(1);\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCMonth(date.getUTCMonth() + step);\n}, (start, end) => {\n  return end.getUTCMonth() - start.getUTCMonth() + (end.getUTCFullYear() - start.getUTCFullYear()) * 12;\n}, (date) => {\n  return date.getUTCMonth();\n});\n\nexport const utcMonths = utcMonth.range;\n"], "mappings": "AAAA,SAAQA,YAAY,QAAO,eAAe;AAE1C,OAAO,MAAMC,SAAS,GAAGD,YAAY,CAAEE,IAAI,IAAK;EAC9CA,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;EACfD,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3B,CAAC,EAAE,CAACF,IAAI,EAAEG,IAAI,KAAK;EACjBH,IAAI,CAACI,QAAQ,CAACJ,IAAI,CAACK,QAAQ,CAAC,CAAC,GAAGF,IAAI,CAAC;AACvC,CAAC,EAAE,CAACG,KAAK,EAAEC,GAAG,KAAK;EACjB,OAAOA,GAAG,CAACF,QAAQ,CAAC,CAAC,GAAGC,KAAK,CAACD,QAAQ,CAAC,CAAC,GAAG,CAACE,GAAG,CAACC,WAAW,CAAC,CAAC,GAAGF,KAAK,CAACE,WAAW,CAAC,CAAC,IAAI,EAAE;AAC3F,CAAC,EAAGR,IAAI,IAAK;EACX,OAAOA,IAAI,CAACK,QAAQ,CAAC,CAAC;AACxB,CAAC,CAAC;AAEF,OAAO,MAAMI,UAAU,GAAGV,SAAS,CAACW,KAAK;AAEzC,OAAO,MAAMC,QAAQ,GAAGb,YAAY,CAAEE,IAAI,IAAK;EAC7CA,IAAI,CAACY,UAAU,CAAC,CAAC,CAAC;EAClBZ,IAAI,CAACa,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9B,CAAC,EAAE,CAACb,IAAI,EAAEG,IAAI,KAAK;EACjBH,IAAI,CAACc,WAAW,CAACd,IAAI,CAACe,WAAW,CAAC,CAAC,GAAGZ,IAAI,CAAC;AAC7C,CAAC,EAAE,CAACG,KAAK,EAAEC,GAAG,KAAK;EACjB,OAAOA,GAAG,CAACQ,WAAW,CAAC,CAAC,GAAGT,KAAK,CAACS,WAAW,CAAC,CAAC,GAAG,CAACR,GAAG,CAACS,cAAc,CAAC,CAAC,GAAGV,KAAK,CAACU,cAAc,CAAC,CAAC,IAAI,EAAE;AACvG,CAAC,EAAGhB,IAAI,IAAK;EACX,OAAOA,IAAI,CAACe,WAAW,CAAC,CAAC;AAC3B,CAAC,CAAC;AAEF,OAAO,MAAME,SAAS,GAAGN,QAAQ,CAACD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}