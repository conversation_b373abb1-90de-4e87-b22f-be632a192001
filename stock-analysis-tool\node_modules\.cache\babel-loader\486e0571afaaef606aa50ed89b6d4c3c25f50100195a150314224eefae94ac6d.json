{"ast": null, "code": "/**\n * @fileOverview 一些公用的运算方法\n * <AUTHOR>\n * @date 2015-09-17\n */\nimport Decimal from 'decimal.js-light';\nimport { curry } from './utils';\n/**\n * 获取数值的位数\n * 其中绝对值属于区间[0.1, 1)， 得到的值为0\n * 绝对值属于区间[0.01, 0.1)，得到的位数为 -1\n * 绝对值属于区间[0.001, 0.01)，得到的位数为 -2\n *\n * @param  {Number} value 数值\n * @return {Integer} 位数\n */\n\nfunction getDigitCount(value) {\n  var result;\n  if (value === 0) {\n    result = 1;\n  } else {\n    result = Math.floor(new Decimal(value).abs().log(10).toNumber()) + 1;\n  }\n  return result;\n}\n/**\n * 按照固定的步长获取[start, end)这个区间的数据\n * 并且需要处理js计算精度的问题\n *\n * @param  {Decimal} start 起点\n * @param  {Decimal} end   终点，不包含该值\n * @param  {Decimal} step  步长\n * @return {Array}         若干数值\n */\n\nfunction rangeStep(start, end, step) {\n  var num = new Decimal(start);\n  var i = 0;\n  var result = []; // magic number to prevent infinite loop\n\n  while (num.lt(end) && i < 100000) {\n    result.push(num.toNumber());\n    num = num.add(step);\n    i++;\n  }\n  return result;\n}\n/**\n * 对数值进行线性插值\n *\n * @param  {Number} a  定义域的极点\n * @param  {Number} b  定义域的极点\n * @param  {Number} t  [0, 1]内的某个值\n * @return {Number}    定义域内的某个值\n */\n\nvar interpolateNumber = curry(function (a, b, t) {\n  var newA = +a;\n  var newB = +b;\n  return newA + t * (newB - newA);\n});\n/**\n * 线性插值的逆运算\n *\n * @param  {Number} a 定义域的极点\n * @param  {Number} b 定义域的极点\n * @param  {Number} x 可以认为是插值后的一个输出值\n * @return {Number}   当x在 a ~ b这个范围内时，返回值属于[0, 1]\n */\n\nvar uninterpolateNumber = curry(function (a, b, x) {\n  var diff = b - +a;\n  diff = diff || Infinity;\n  return (x - a) / diff;\n});\n/**\n * 线性插值的逆运算，并且有截断的操作\n *\n * @param  {Number} a 定义域的极点\n * @param  {Number} b 定义域的极点\n * @param  {Number} x 可以认为是插值后的一个输出值\n * @return {Number}   当x在 a ~ b这个区间内时，返回值属于[0, 1]，\n * 当x不在 a ~ b这个区间时，会截断到 a ~ b 这个区间\n */\n\nvar uninterpolateTruncation = curry(function (a, b, x) {\n  var diff = b - +a;\n  diff = diff || Infinity;\n  return Math.max(0, Math.min(1, (x - a) / diff));\n});\nexport default {\n  rangeStep: rangeStep,\n  getDigitCount: getDigitCount,\n  interpolateNumber: interpolateNumber,\n  uninterpolateNumber: uninterpolateNumber,\n  uninterpolateTruncation: uninterpolateTruncation\n};", "map": {"version": 3, "names": ["Decimal", "curry", "getDigitCount", "value", "result", "Math", "floor", "abs", "log", "toNumber", "rangeStep", "start", "end", "step", "num", "i", "lt", "push", "add", "interpolateNumber", "a", "b", "t", "newA", "newB", "uninterpolateNumber", "x", "diff", "Infinity", "uninterpolateTruncation", "max", "min"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/recharts-scale/es6/util/arithmetic.js"], "sourcesContent": ["/**\n * @fileOverview 一些公用的运算方法\n * <AUTHOR>\n * @date 2015-09-17\n */\nimport Decimal from 'decimal.js-light';\nimport { curry } from './utils';\n/**\n * 获取数值的位数\n * 其中绝对值属于区间[0.1, 1)， 得到的值为0\n * 绝对值属于区间[0.01, 0.1)，得到的位数为 -1\n * 绝对值属于区间[0.001, 0.01)，得到的位数为 -2\n *\n * @param  {Number} value 数值\n * @return {Integer} 位数\n */\n\nfunction getDigitCount(value) {\n  var result;\n\n  if (value === 0) {\n    result = 1;\n  } else {\n    result = Math.floor(new Decimal(value).abs().log(10).toNumber()) + 1;\n  }\n\n  return result;\n}\n/**\n * 按照固定的步长获取[start, end)这个区间的数据\n * 并且需要处理js计算精度的问题\n *\n * @param  {Decimal} start 起点\n * @param  {Decimal} end   终点，不包含该值\n * @param  {Decimal} step  步长\n * @return {Array}         若干数值\n */\n\n\nfunction rangeStep(start, end, step) {\n  var num = new Decimal(start);\n  var i = 0;\n  var result = []; // magic number to prevent infinite loop\n\n  while (num.lt(end) && i < 100000) {\n    result.push(num.toNumber());\n    num = num.add(step);\n    i++;\n  }\n\n  return result;\n}\n/**\n * 对数值进行线性插值\n *\n * @param  {Number} a  定义域的极点\n * @param  {Number} b  定义域的极点\n * @param  {Number} t  [0, 1]内的某个值\n * @return {Number}    定义域内的某个值\n */\n\n\nvar interpolateNumber = curry(function (a, b, t) {\n  var newA = +a;\n  var newB = +b;\n  return newA + t * (newB - newA);\n});\n/**\n * 线性插值的逆运算\n *\n * @param  {Number} a 定义域的极点\n * @param  {Number} b 定义域的极点\n * @param  {Number} x 可以认为是插值后的一个输出值\n * @return {Number}   当x在 a ~ b这个范围内时，返回值属于[0, 1]\n */\n\nvar uninterpolateNumber = curry(function (a, b, x) {\n  var diff = b - +a;\n  diff = diff || Infinity;\n  return (x - a) / diff;\n});\n/**\n * 线性插值的逆运算，并且有截断的操作\n *\n * @param  {Number} a 定义域的极点\n * @param  {Number} b 定义域的极点\n * @param  {Number} x 可以认为是插值后的一个输出值\n * @return {Number}   当x在 a ~ b这个区间内时，返回值属于[0, 1]，\n * 当x不在 a ~ b这个区间时，会截断到 a ~ b 这个区间\n */\n\nvar uninterpolateTruncation = curry(function (a, b, x) {\n  var diff = b - +a;\n  diff = diff || Infinity;\n  return Math.max(0, Math.min(1, (x - a) / diff));\n});\nexport default {\n  rangeStep: rangeStep,\n  getDigitCount: getDigitCount,\n  interpolateNumber: interpolateNumber,\n  uninterpolateNumber: uninterpolateNumber,\n  uninterpolateTruncation: uninterpolateTruncation\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAOA,OAAO,MAAM,kBAAkB;AACtC,SAASC,KAAK,QAAQ,SAAS;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,IAAIC,MAAM;EAEV,IAAID,KAAK,KAAK,CAAC,EAAE;IACfC,MAAM,GAAG,CAAC;EACZ,CAAC,MAAM;IACLA,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAIN,OAAO,CAACG,KAAK,CAAC,CAACI,GAAG,CAAC,CAAC,CAACC,GAAG,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;EACtE;EAEA,OAAOL,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASM,SAASA,CAACC,KAAK,EAAEC,GAAG,EAAEC,IAAI,EAAE;EACnC,IAAIC,GAAG,GAAG,IAAId,OAAO,CAACW,KAAK,CAAC;EAC5B,IAAII,CAAC,GAAG,CAAC;EACT,IAAIX,MAAM,GAAG,EAAE,CAAC,CAAC;;EAEjB,OAAOU,GAAG,CAACE,EAAE,CAACJ,GAAG,CAAC,IAAIG,CAAC,GAAG,MAAM,EAAE;IAChCX,MAAM,CAACa,IAAI,CAACH,GAAG,CAACL,QAAQ,CAAC,CAAC,CAAC;IAC3BK,GAAG,GAAGA,GAAG,CAACI,GAAG,CAACL,IAAI,CAAC;IACnBE,CAAC,EAAE;EACL;EAEA,OAAOX,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,IAAIe,iBAAiB,GAAGlB,KAAK,CAAC,UAAUmB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC/C,IAAIC,IAAI,GAAG,CAACH,CAAC;EACb,IAAII,IAAI,GAAG,CAACH,CAAC;EACb,OAAOE,IAAI,GAAGD,CAAC,IAAIE,IAAI,GAAGD,IAAI,CAAC;AACjC,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIE,mBAAmB,GAAGxB,KAAK,CAAC,UAAUmB,CAAC,EAAEC,CAAC,EAAEK,CAAC,EAAE;EACjD,IAAIC,IAAI,GAAGN,CAAC,GAAG,CAACD,CAAC;EACjBO,IAAI,GAAGA,IAAI,IAAIC,QAAQ;EACvB,OAAO,CAACF,CAAC,GAAGN,CAAC,IAAIO,IAAI;AACvB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIE,uBAAuB,GAAG5B,KAAK,CAAC,UAAUmB,CAAC,EAAEC,CAAC,EAAEK,CAAC,EAAE;EACrD,IAAIC,IAAI,GAAGN,CAAC,GAAG,CAACD,CAAC;EACjBO,IAAI,GAAGA,IAAI,IAAIC,QAAQ;EACvB,OAAOvB,IAAI,CAACyB,GAAG,CAAC,CAAC,EAAEzB,IAAI,CAAC0B,GAAG,CAAC,CAAC,EAAE,CAACL,CAAC,GAAGN,CAAC,IAAIO,IAAI,CAAC,CAAC;AACjD,CAAC,CAAC;AACF,eAAe;EACbjB,SAAS,EAAEA,SAAS;EACpBR,aAAa,EAAEA,aAAa;EAC5BiB,iBAAiB,EAAEA,iBAAiB;EACpCM,mBAAmB,EAAEA,mBAAmB;EACxCI,uBAAuB,EAAEA;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}