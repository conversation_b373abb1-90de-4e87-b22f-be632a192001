{"ast": null, "code": "import { ascending, bisect, quantile } from \"d3-array\";\nimport { identity } from \"./continuous.js\";\nimport { initInterpolator } from \"./init.js\";\nexport default function sequentialQuantile() {\n  var domain = [],\n    interpolator = identity;\n  function scale(x) {\n    if (x != null && !isNaN(x = +x)) return interpolator((bisect(domain, x, 1) - 1) / (domain.length - 1));\n  }\n  scale.domain = function (_) {\n    if (!arguments.length) return domain.slice();\n    domain = [];\n    for (let d of _) if (d != null && !isNaN(d = +d)) domain.push(d);\n    domain.sort(ascending);\n    return scale;\n  };\n  scale.interpolator = function (_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n  scale.range = function () {\n    return domain.map((d, i) => interpolator(i / (domain.length - 1)));\n  };\n  scale.quantiles = function (n) {\n    return Array.from({\n      length: n + 1\n    }, (_, i) => quantile(domain, i / n));\n  };\n  scale.copy = function () {\n    return sequentialQuantile(interpolator).domain(domain);\n  };\n  return initInterpolator.apply(scale, arguments);\n}", "map": {"version": 3, "names": ["ascending", "bisect", "quantile", "identity", "initInterpolator", "sequentialQuantile", "domain", "interpolator", "scale", "x", "isNaN", "length", "_", "arguments", "slice", "d", "push", "sort", "range", "map", "i", "quantiles", "n", "Array", "from", "copy", "apply"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/d3-scale/src/sequentialQuantile.js"], "sourcesContent": ["import {ascending, bisect, quantile} from \"d3-array\";\nimport {identity} from \"./continuous.js\";\nimport {initInterpolator} from \"./init.js\";\n\nexport default function sequentialQuantile() {\n  var domain = [],\n      interpolator = identity;\n\n  function scale(x) {\n    if (x != null && !isNaN(x = +x)) return interpolator((bisect(domain, x, 1) - 1) / (domain.length - 1));\n  }\n\n  scale.domain = function(_) {\n    if (!arguments.length) return domain.slice();\n    domain = [];\n    for (let d of _) if (d != null && !isNaN(d = +d)) domain.push(d);\n    domain.sort(ascending);\n    return scale;\n  };\n\n  scale.interpolator = function(_) {\n    return arguments.length ? (interpolator = _, scale) : interpolator;\n  };\n\n  scale.range = function() {\n    return domain.map((d, i) => interpolator(i / (domain.length - 1)));\n  };\n\n  scale.quantiles = function(n) {\n    return Array.from({length: n + 1}, (_, i) => quantile(domain, i / n));\n  };\n\n  scale.copy = function() {\n    return sequentialQuantile(interpolator).domain(domain);\n  };\n\n  return initInterpolator.apply(scale, arguments);\n}\n"], "mappings": "AAAA,SAAQA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAO,UAAU;AACpD,SAAQC,QAAQ,QAAO,iBAAiB;AACxC,SAAQC,gBAAgB,QAAO,WAAW;AAE1C,eAAe,SAASC,kBAAkBA,CAAA,EAAG;EAC3C,IAAIC,MAAM,GAAG,EAAE;IACXC,YAAY,GAAGJ,QAAQ;EAE3B,SAASK,KAAKA,CAACC,CAAC,EAAE;IAChB,IAAIA,CAAC,IAAI,IAAI,IAAI,CAACC,KAAK,CAACD,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,OAAOF,YAAY,CAAC,CAACN,MAAM,CAACK,MAAM,EAAEG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAKH,MAAM,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC;EACxG;EAEAH,KAAK,CAACF,MAAM,GAAG,UAASM,CAAC,EAAE;IACzB,IAAI,CAACC,SAAS,CAACF,MAAM,EAAE,OAAOL,MAAM,CAACQ,KAAK,CAAC,CAAC;IAC5CR,MAAM,GAAG,EAAE;IACX,KAAK,IAAIS,CAAC,IAAIH,CAAC,EAAE,IAAIG,CAAC,IAAI,IAAI,IAAI,CAACL,KAAK,CAACK,CAAC,GAAG,CAACA,CAAC,CAAC,EAAET,MAAM,CAACU,IAAI,CAACD,CAAC,CAAC;IAChET,MAAM,CAACW,IAAI,CAACjB,SAAS,CAAC;IACtB,OAAOQ,KAAK;EACd,CAAC;EAEDA,KAAK,CAACD,YAAY,GAAG,UAASK,CAAC,EAAE;IAC/B,OAAOC,SAAS,CAACF,MAAM,IAAIJ,YAAY,GAAGK,CAAC,EAAEJ,KAAK,IAAID,YAAY;EACpE,CAAC;EAEDC,KAAK,CAACU,KAAK,GAAG,YAAW;IACvB,OAAOZ,MAAM,CAACa,GAAG,CAAC,CAACJ,CAAC,EAAEK,CAAC,KAAKb,YAAY,CAACa,CAAC,IAAId,MAAM,CAACK,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;EACpE,CAAC;EAEDH,KAAK,CAACa,SAAS,GAAG,UAASC,CAAC,EAAE;IAC5B,OAAOC,KAAK,CAACC,IAAI,CAAC;MAACb,MAAM,EAAEW,CAAC,GAAG;IAAC,CAAC,EAAE,CAACV,CAAC,EAAEQ,CAAC,KAAKlB,QAAQ,CAACI,MAAM,EAAEc,CAAC,GAAGE,CAAC,CAAC,CAAC;EACvE,CAAC;EAEDd,KAAK,CAACiB,IAAI,GAAG,YAAW;IACtB,OAAOpB,kBAAkB,CAACE,YAAY,CAAC,CAACD,MAAM,CAACA,MAAM,CAAC;EACxD,CAAC;EAED,OAAOF,gBAAgB,CAACsB,KAAK,CAAClB,KAAK,EAAEK,SAAS,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}