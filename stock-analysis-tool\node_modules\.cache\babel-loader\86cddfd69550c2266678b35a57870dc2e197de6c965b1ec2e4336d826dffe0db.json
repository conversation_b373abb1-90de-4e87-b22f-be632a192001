{"ast": null, "code": "import { lab as colorLab } from \"d3-color\";\nimport color from \"./color.js\";\nexport default function lab(start, end) {\n  var l = color((start = colorLab(start)).l, (end = colorLab(end)).l),\n    a = color(start.a, end.a),\n    b = color(start.b, end.b),\n    opacity = color(start.opacity, end.opacity);\n  return function (t) {\n    start.l = l(t);\n    start.a = a(t);\n    start.b = b(t);\n    start.opacity = opacity(t);\n    return start + \"\";\n  };\n}", "map": {"version": 3, "names": ["lab", "colorLab", "color", "start", "end", "l", "a", "b", "opacity", "t"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/d3-interpolate/src/lab.js"], "sourcesContent": ["import {lab as colorLab} from \"d3-color\";\nimport color from \"./color.js\";\n\nexport default function lab(start, end) {\n  var l = color((start = colorLab(start)).l, (end = colorLab(end)).l),\n      a = color(start.a, end.a),\n      b = color(start.b, end.b),\n      opacity = color(start.opacity, end.opacity);\n  return function(t) {\n    start.l = l(t);\n    start.a = a(t);\n    start.b = b(t);\n    start.opacity = opacity(t);\n    return start + \"\";\n  };\n}\n"], "mappings": "AAAA,SAAQA,GAAG,IAAIC,QAAQ,QAAO,UAAU;AACxC,OAAOC,KAAK,MAAM,YAAY;AAE9B,eAAe,SAASF,GAAGA,CAACG,KAAK,EAAEC,GAAG,EAAE;EACtC,IAAIC,CAAC,GAAGH,KAAK,CAAC,CAACC,KAAK,GAAGF,QAAQ,CAACE,KAAK,CAAC,EAAEE,CAAC,EAAE,CAACD,GAAG,GAAGH,QAAQ,CAACG,GAAG,CAAC,EAAEC,CAAC,CAAC;IAC/DC,CAAC,GAAGJ,KAAK,CAACC,KAAK,CAACG,CAAC,EAAEF,GAAG,CAACE,CAAC,CAAC;IACzBC,CAAC,GAAGL,KAAK,CAACC,KAAK,CAACI,CAAC,EAAEH,GAAG,CAACG,CAAC,CAAC;IACzBC,OAAO,GAAGN,KAAK,CAACC,KAAK,CAACK,OAAO,EAAEJ,GAAG,CAACI,OAAO,CAAC;EAC/C,OAAO,UAASC,CAAC,EAAE;IACjBN,KAAK,CAACE,CAAC,GAAGA,CAAC,CAACI,CAAC,CAAC;IACdN,KAAK,CAACG,CAAC,GAAGA,CAAC,CAACG,CAAC,CAAC;IACdN,KAAK,CAACI,CAAC,GAAGA,CAAC,CAACE,CAAC,CAAC;IACdN,KAAK,CAACK,OAAO,GAAGA,OAAO,CAACC,CAAC,CAAC;IAC1B,OAAON,KAAK,GAAG,EAAE;EACnB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}