<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全球股票分析工具</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 32px;
        }
        .header h1 {
            font-size: 42px;
            margin-bottom: 16px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .search-box {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;
        }
        .search-input {
            flex: 1;
            padding: 16px 20px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 16px;
            outline: none;
        }
        .search-input:focus {
            border-color: #667eea;
        }
        .btn {
            padding: 16px 32px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        .btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
        }
        .suggestions {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            margin-top: 8px;
            max-height: 300px;
            overflow-y: auto;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .suggestion-item {
            padding: 16px;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .suggestion-item:hover {
            background-color: #f8fafc;
        }
        .suggestion-item:last-child {
            border-bottom: none;
        }
        .stock-info {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 24px;
            align-items: start;
            margin-bottom: 24px;
        }
        .stock-name {
            font-size: 28px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 8px;
        }
        .stock-details {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-bottom: 16px;
        }
        .tag {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 14px;
        }
        .price-info {
            text-align: right;
        }
        .current-price {
            font-size: 36px;
            font-weight: bold;
            color: #111827;
        }
        .price-change {
            font-size: 16px;
            font-weight: 600;
            margin-top: 8px;
        }
        .price-change.positive { color: #059669; }
        .price-change.negative { color: #dc2626; }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
            padding: 16px;
            background: rgba(103, 126, 234, 0.05);
            border-radius: 12px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-label {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 4px;
        }
        .stat-value {
            font-weight: 700;
            font-size: 16px;
            color: #111827;
        }
        .tabs {
            display: flex;
            background: #f8fafc;
            border-radius: 12px 12px 0 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .tab {
            flex: 1;
            padding: 16px 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.3s ease;
        }
        .tab.active {
            background: white;
            color: #667eea;
            font-weight: 600;
            border-bottom: 2px solid #667eea;
        }
        .tab-content {
            padding: 32px;
            background: white;
            border-radius: 0 0 12px 12px;
        }
        .prediction-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 16px;
            margin-top: 24px;
        }
        .prediction-card {
            padding: 16px;
            background: #f8fafc;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #e2e8f0;
        }
        .prediction-day {
            color: #6b7280;
            font-size: 12px;
            margin-bottom: 8px;
        }
        .prediction-price {
            font-weight: bold;
            font-size: 18px;
            color: #111827;
            margin-bottom: 4px;
        }
        .prediction-change {
            font-size: 12px;
            font-weight: 600;
        }
        .prediction-change.positive { color: #059669; }
        .prediction-change.negative { color: #dc2626; }
        .loading {
            text-align: center;
            padding: 48px;
        }
        .spinner {
            width: 48px;
            height: 48px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .empty-state {
            text-align: center;
            padding: 64px;
            color: #6b7280;
        }
        .empty-state h3 {
            font-size: 24px;
            margin-bottom: 16px;
            color: #374151;
        }
        .example-stocks {
            display: flex;
            justify-content: center;
            gap: 12px;
            flex-wrap: wrap;
            margin-top: 24px;
        }
        .example-btn {
            padding: 8px 16px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .example-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        .advice-card {
            padding: 24px;
            border-radius: 12px;
            margin-bottom: 24px;
            text-align: center;
        }
        .advice-card.buy {
            background: #f0fdf4;
            border: 2px solid #10b981;
        }
        .advice-card.sell {
            background: #fef2f2;
            border: 2px solid #ef4444;
        }
        .advice-card.hold {
            background: #f8fafc;
            border: 2px solid #6b7280;
        }
        .advice-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 16px;
        }
        .advice-title.buy { color: #059669; }
        .advice-title.sell { color: #dc2626; }
        .advice-title.hold { color: #6b7280; }
        .advice-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        .hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌍 全球股票分析工具</h1>
            <p>基于LSTM深度学习的全球股票实时分析与AI预测系统</p>
        </div>

        <div class="card">
            <h3 style="margin-bottom: 16px; color: #667eea;">🔍 全球股票搜索</h3>
            <p style="color: #6b7280; margin-bottom: 24px;">搜索全球任意上市公司股票，支持股票代码、公司名称、行业关键词</p>
            
            <div class="search-box">
                <div style="flex: 1; position: relative;">
                    <input type="text" id="searchInput" class="search-input" 
                           placeholder="输入股票代码 (AAPL, TSLA) 或公司名称 (Apple, Tesla) 或行业 (Technology)">
                    <div id="suggestions" class="suggestions hidden"></div>
                </div>
                <button id="searchBtn" class="btn">搜索</button>
            </div>
        </div>

        <div id="stockCard" class="card hidden">
            <div class="stock-info">
                <div>
                    <div id="stockName" class="stock-name"></div>
                    <div id="stockDetails" class="stock-details"></div>
                </div>
                <div class="price-info">
                    <div id="currentPrice" class="current-price"></div>
                    <div id="priceChange" class="price-change"></div>
                </div>
            </div>
            <div id="statsGrid" class="stats-grid"></div>
        </div>

        <div id="tabsCard" class="card hidden">
            <div class="tabs">
                <button class="tab active" onclick="showTab('overview')">📊 概览</button>
                <button class="tab" onclick="showTab('technical')">📈 技术分析</button>
                <button class="tab" onclick="showTab('prediction')">🧠 AI预测</button>
                <button class="tab" onclick="showTab('advice')">💡 投资建议</button>
            </div>
            <div class="tab-content">
                <div id="overviewTab">
                    <h3>📊 股票概览</h3>
                    <p style="color: #6b7280; margin-top: 16px;">股票基本信息和历史表现数据</p>
                </div>
                <div id="technicalTab" class="hidden">
                    <h3>📈 技术分析</h3>
                    <div id="technicalIndicators" style="margin-top: 24px;"></div>
                </div>
                <div id="predictionTab" class="hidden">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
                        <h3>🧠 LSTM AI预测</h3>
                        <button id="predictBtn" class="btn" onclick="runPrediction()">运行AI预测</button>
                    </div>
                    <div id="predictionContent">
                        <p style="color: #6b7280;">点击"运行AI预测"开始分析</p>
                    </div>
                </div>
                <div id="adviceTab" class="hidden">
                    <h3>💡 投资建议</h3>
                    <div id="adviceContent">
                        <p style="color: #6b7280; margin-top: 16px;">请先运行AI预测以获取投资建议</p>
                    </div>
                </div>
            </div>
        </div>

        <div id="loadingCard" class="card hidden">
            <div class="loading">
                <div class="spinner"></div>
                <p>正在获取股票数据...</p>
            </div>
        </div>

        <div id="emptyState" class="card">
            <div class="empty-state">
                <div style="font-size: 72px; margin-bottom: 24px;">🔍</div>
                <h3>开始全球股票分析</h3>
                <p>在上方搜索框中输入任意股票代码或公司名称开始分析</p>
                <div class="example-stocks">
                    <button class="example-btn" onclick="searchStock('AAPL')">AAPL</button>
                    <button class="example-btn" onclick="searchStock('TSLA')">TSLA</button>
                    <button class="example-btn" onclick="searchStock('MSFT')">MSFT</button>
                    <button class="example-btn" onclick="searchStock('GOOGL')">GOOGL</button>
                    <button class="example-btn" onclick="searchStock('BABA')">BABA</button>
                    <button class="example-btn" onclick="searchStock('NVDA')">NVDA</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全球股票数据库
        const GLOBAL_STOCKS = [
            { symbol: 'AAPL', name: 'Apple Inc.', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Technology' },
            { symbol: 'MSFT', name: 'Microsoft Corporation', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Technology' },
            { symbol: 'GOOGL', name: 'Alphabet Inc.', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Technology' },
            { symbol: 'AMZN', name: 'Amazon.com Inc.', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Consumer Cyclical' },
            { symbol: 'TSLA', name: 'Tesla Inc.', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Consumer Cyclical' },
            { symbol: 'META', name: 'Meta Platforms Inc.', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Technology' },
            { symbol: 'NVDA', name: 'NVIDIA Corporation', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Technology' },
            { symbol: 'NFLX', name: 'Netflix Inc.', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Communication Services' },
            { symbol: 'JPM', name: 'JPMorgan Chase & Co.', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Financial Services' },
            { symbol: 'BAC', name: 'Bank of America Corp.', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Financial Services' },
            { symbol: 'BABA', name: 'Alibaba Group Holding Limited', exchange: 'NYSE', region: 'China', currency: 'USD', sector: 'Consumer Cyclical' },
            { symbol: 'TCEHY', name: 'Tencent Holdings Limited', exchange: 'OTC', region: 'China', currency: 'USD', sector: 'Technology' },
            { symbol: 'JD', name: 'JD.com Inc.', exchange: 'NASDAQ', region: 'China', currency: 'USD', sector: 'Consumer Cyclical' },
            { symbol: 'NIO', name: 'NIO Inc.', exchange: 'NYSE', region: 'China', currency: 'USD', sector: 'Consumer Cyclical' },
            { symbol: 'ASML', name: 'ASML Holding N.V.', exchange: 'NASDAQ', region: 'Netherlands', currency: 'USD', sector: 'Technology' },
            { symbol: 'SAP', name: 'SAP SE', exchange: 'NYSE', region: 'Germany', currency: 'USD', sector: 'Technology' },
            { symbol: 'TSM', name: 'Taiwan Semiconductor Manufacturing', exchange: 'NYSE', region: 'Taiwan', currency: 'USD', sector: 'Technology' },
            { symbol: 'SONY', name: 'Sony Group Corporation', exchange: 'NYSE', region: 'Japan', currency: 'USD', sector: 'Technology' },
            { symbol: 'V', name: 'Visa Inc.', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Financial Services' },
            { symbol: 'MA', name: 'Mastercard Incorporated', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Financial Services' },
            { symbol: 'JNJ', name: 'Johnson & Johnson', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Healthcare' },
            { symbol: 'PFE', name: 'Pfizer Inc.', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Healthcare' },
            { symbol: 'KO', name: 'The Coca-Cola Company', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Consumer Defensive' },
            { symbol: 'PEP', name: 'PepsiCo Inc.', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Consumer Defensive' },
            { symbol: 'WMT', name: 'Walmart Inc.', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Consumer Defensive' },
            { symbol: 'DIS', name: 'The Walt Disney Company', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Communication Services' },
            { symbol: 'ADBE', name: 'Adobe Inc.', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Technology' },
            { symbol: 'CRM', name: 'Salesforce Inc.', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Technology' },
            { symbol: 'ORCL', name: 'Oracle Corporation', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Technology' },
            { symbol: 'IBM', name: 'International Business Machines', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Technology' }
        ];

        let currentStock = null;
        let currentPredictions = null;
        let currentTechnicalData = null;

        // 搜索功能
        function searchStocks(query) {
            if (!query || query.length < 1) return [];
            const lowerQuery = query.toLowerCase();
            return GLOBAL_STOCKS.filter(stock =>
                stock.symbol.toLowerCase().includes(lowerQuery) ||
                stock.name.toLowerCase().includes(lowerQuery) ||
                stock.sector.toLowerCase().includes(lowerQuery) ||
                stock.region.toLowerCase().includes(lowerQuery)
            ).slice(0, 8);
        }

        // 生成股票数据
        function generateStockData(symbol) {
            const stockInfo = GLOBAL_STOCKS.find(s => s.symbol === symbol) || GLOBAL_STOCKS[0];
            const basePrice = 50 + Math.random() * 500;
            const change = (Math.random() - 0.5) * basePrice * 0.08;
            const changePercent = (change / basePrice) * 100;

            return {
                symbol: symbol,
                name: stockInfo.name,
                price: parseFloat(basePrice.toFixed(2)),
                change: parseFloat(change.toFixed(2)),
                changePercent: parseFloat(changePercent.toFixed(2)),
                volume: Math.floor(Math.random() * 100000000) + 1000000,
                marketCap: Math.floor(Math.random() * 2000000000000) + 10000000000,
                pe: parseFloat((15 + Math.random() * 40).toFixed(2)),
                high52: parseFloat((basePrice * (1.3 + Math.random() * 0.7)).toFixed(2)),
                low52: parseFloat((basePrice * (0.4 + Math.random() * 0.4)).toFixed(2)),
                currency: stockInfo.currency,
                exchange: stockInfo.exchange,
                sector: stockInfo.sector,
                region: stockInfo.region,
                lastUpdate: new Date().toISOString()
            };
        }

        // 生成技术指标
        function generateTechnicalData() {
            const rsi = 30 + Math.random() * 40;
            return {
                sma20: (currentStock.price * (0.95 + Math.random() * 0.1)).toFixed(2),
                sma50: (currentStock.price * (0.90 + Math.random() * 0.2)).toFixed(2),
                rsi: rsi.toFixed(2),
                rsiStatus: rsi < 30 ? '超卖' : rsi > 70 ? '超买' : '中性',
                macd: (Math.random() * 2 - 1).toFixed(3),
                macdSignal: (Math.random() * 2 - 1).toFixed(3)
            };
        }

        // 生成AI预测
        function generatePredictions() {
            const predictions = [];
            let price = currentStock.price;

            for (let i = 1; i <= 10; i++) {
                const change = (Math.random() - 0.5) * 0.05;
                price *= (1 + change);
                const confidence = Math.max(0.5, 0.95 - i * 0.05);
                const priceChange = ((price - currentStock.price) / currentStock.price * 100);

                predictions.push({
                    day: i,
                    price: parseFloat(price.toFixed(2)),
                    confidence: parseFloat(confidence.toFixed(3)),
                    change: parseFloat(priceChange.toFixed(2))
                });
            }

            return predictions;
        }

        // 生成投资建议
        function generateAdvice() {
            const rsi = parseFloat(currentTechnicalData.rsi);
            const prediction = currentPredictions[0];

            let score = 0;
            let signals = [];

            if (rsi < 30) {
                signals.push('RSI超卖，可能反弹');
                score += 2;
            } else if (rsi > 70) {
                signals.push('RSI超买，注意回调风险');
                score -= 2;
            } else {
                signals.push('RSI中性');
            }

            if (parseFloat(currentTechnicalData.macd) > parseFloat(currentTechnicalData.macdSignal)) {
                signals.push('MACD金叉，趋势向好');
                score += 1;
            } else {
                signals.push('MACD死叉，趋势偏弱');
                score -= 1;
            }

            if (prediction.change > 5) {
                signals.push('AI预测价格将大幅上涨');
                score += 2;
            } else if (prediction.change < -5) {
                signals.push('AI预测价格将大幅下跌');
                score -= 2;
            }

            let recommendation, type, targetPrice, stopLoss;
            if (score >= 3) {
                recommendation = '强烈买入';
                type = 'buy';
                targetPrice = currentStock.price * 1.15;
                stopLoss = currentStock.price * 0.92;
            } else if (score >= 1) {
                recommendation = '买入';
                type = 'buy';
                targetPrice = currentStock.price * 1.10;
                stopLoss = currentStock.price * 0.95;
            } else if (score <= -3) {
                recommendation = '强烈卖出';
                type = 'sell';
                targetPrice = currentStock.price * 0.85;
                stopLoss = currentStock.price * 1.08;
            } else if (score <= -1) {
                recommendation = '卖出';
                type = 'sell';
                targetPrice = currentStock.price * 0.90;
                stopLoss = currentStock.price * 1.05;
            } else {
                recommendation = '持有';
                type = 'hold';
                targetPrice = currentStock.price * 1.05;
                stopLoss = currentStock.price * 0.95;
            }

            return {
                recommendation,
                type,
                score,
                signals,
                targetPrice: parseFloat(targetPrice.toFixed(2)),
                stopLoss: parseFloat(stopLoss.toFixed(2)),
                confidence: Math.min(0.95, 0.6 + Math.abs(score) * 0.1)
            };
        }

        // 显示建议
        function showSuggestions(results) {
            const suggestionsDiv = document.getElementById('suggestions');
            if (results.length === 0) {
                suggestionsDiv.classList.add('hidden');
                return;
            }

            suggestionsDiv.innerHTML = results.map(stock => `
                <div class="suggestion-item" onclick="selectStock('${stock.symbol}')">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="font-weight: bold; font-size: 16px; color: #111827;">${stock.symbol}</div>
                            <div style="color: #6b7280; font-size: 14px; margin-top: 2px;">${stock.name}</div>
                            <div style="color: #9ca3af; font-size: 12px; margin-top: 4px;">${stock.exchange} • ${stock.region} • ${stock.sector}</div>
                        </div>
                        <div style="background: #667eea; color: white; padding: 4px 8px; border-radius: 6px; font-size: 12px;">
                            ${stock.currency}
                        </div>
                    </div>
                </div>
            `).join('');

            suggestionsDiv.classList.remove('hidden');
        }

        // 选择股票
        async function selectStock(symbol) {
            document.getElementById('suggestions').classList.add('hidden');
            document.getElementById('emptyState').classList.add('hidden');
            document.getElementById('stockCard').classList.add('hidden');
            document.getElementById('tabsCard').classList.add('hidden');
            document.getElementById('loadingCard').classList.remove('hidden');

            // 模拟加载时间
            await new Promise(resolve => setTimeout(resolve, 1500));

            currentStock = generateStockData(symbol);
            currentTechnicalData = generateTechnicalData();

            displayStockInfo();
            displayTechnicalAnalysis();

            document.getElementById('loadingCard').classList.add('hidden');
            document.getElementById('stockCard').classList.remove('hidden');
            document.getElementById('tabsCard').classList.remove('hidden');

            // 重置标签页
            showTab('overview');
            document.getElementById('predictionContent').innerHTML = '<p style="color: #6b7280;">点击"运行AI预测"开始分析</p>';
            document.getElementById('adviceContent').innerHTML = '<p style="color: #6b7280; margin-top: 16px;">请先运行AI预测以获取投资建议</p>';
        }

        // 显示股票信息
        function displayStockInfo() {
            document.getElementById('stockName').textContent = currentStock.name;
            document.getElementById('stockDetails').innerHTML = `
                <span class="tag">${currentStock.symbol}</span>
                <span style="color: #6b7280; font-size: 14px;">${currentStock.exchange}</span>
                <span style="color: #6b7280; font-size: 14px;">${currentStock.currency}</span>
                <span style="background: #10b981; color: white; padding: 2px 6px; border-radius: 4px; font-size: 12px;">实时模拟数据</span>
            `;

            document.getElementById('currentPrice').textContent = `$${currentStock.price}`;

            const changeElement = document.getElementById('priceChange');
            changeElement.textContent = `${currentStock.change >= 0 ? '+' : ''}${currentStock.change} (${currentStock.changePercent >= 0 ? '+' : ''}${currentStock.changePercent}%)`;
            changeElement.className = `price-change ${currentStock.changePercent >= 0 ? 'positive' : 'negative'}`;

            document.getElementById('statsGrid').innerHTML = `
                <div class="stat-item">
                    <div class="stat-label">市值</div>
                    <div class="stat-value">$${(currentStock.marketCap / 1e9).toFixed(1)}B</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">P/E比率</div>
                    <div class="stat-value">${currentStock.pe}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">成交量</div>
                    <div class="stat-value">${(currentStock.volume / 1e6).toFixed(1)}M</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">52周区间</div>
                    <div class="stat-value">$${currentStock.low52} - $${currentStock.high52}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">行业</div>
                    <div class="stat-value">${currentStock.sector}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">更新时间</div>
                    <div class="stat-value">${new Date(currentStock.lastUpdate).toLocaleTimeString()}</div>
                </div>
            `;
        }

        // 显示技术分析
        function displayTechnicalAnalysis() {
            document.getElementById('technicalIndicators').innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 24px;">
                    <div style="padding: 20px; background: #f8fafc; border-radius: 12px; border: 1px solid #e2e8f0;">
                        <h4 style="color: #667eea; margin-bottom: 16px;">移动平均线</h4>
                        <div style="margin-bottom: 8px;">
                            <span style="color: #6b7280;">SMA 20:</span>
                            <span style="font-weight: bold; margin-left: 8px;">$${currentTechnicalData.sma20}</span>
                        </div>
                        <div>
                            <span style="color: #6b7280;">SMA 50:</span>
                            <span style="font-weight: bold; margin-left: 8px;">$${currentTechnicalData.sma50}</span>
                        </div>
                    </div>
                    <div style="padding: 20px; background: #f8fafc; border-radius: 12px; border: 1px solid #e2e8f0;">
                        <h4 style="color: #667eea; margin-bottom: 16px;">RSI指标</h4>
                        <div style="margin-bottom: 8px;">
                            <span style="color: #6b7280;">RSI:</span>
                            <span style="font-weight: bold; margin-left: 8px;">${currentTechnicalData.rsi}</span>
                        </div>
                        <div>
                            <span style="color: #6b7280;">状态:</span>
                            <span style="font-weight: bold; margin-left: 8px; color: ${currentTechnicalData.rsiStatus === '超买' ? '#dc2626' : currentTechnicalData.rsiStatus === '超卖' ? '#059669' : '#6b7280'};">${currentTechnicalData.rsiStatus}</span>
                        </div>
                    </div>
                    <div style="padding: 20px; background: #f8fafc; border-radius: 12px; border: 1px solid #e2e8f0;">
                        <h4 style="color: #667eea; margin-bottom: 16px;">MACD指标</h4>
                        <div style="margin-bottom: 8px;">
                            <span style="color: #6b7280;">MACD:</span>
                            <span style="font-weight: bold; margin-left: 8px;">${currentTechnicalData.macd}</span>
                        </div>
                        <div>
                            <span style="color: #6b7280;">信号线:</span>
                            <span style="font-weight: bold; margin-left: 8px;">${currentTechnicalData.macdSignal}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 运行AI预测
        async function runPrediction() {
            const predictBtn = document.getElementById('predictBtn');
            const predictionContent = document.getElementById('predictionContent');

            predictBtn.disabled = true;
            predictBtn.textContent = '正在分析...';

            predictionContent.innerHTML = `
                <div style="text-align: center; padding: 48px;">
                    <div class="spinner"></div>
                    <p>LSTM神经网络正在分析历史数据...</p>
                </div>
            `;

            // 模拟AI计算时间
            await new Promise(resolve => setTimeout(resolve, 3000));

            currentPredictions = generatePredictions();

            predictionContent.innerHTML = `
                <div style="margin-bottom: 24px;">
                    <h4 style="color: #667eea; margin-bottom: 16px;">📈 未来10日价格预测</h4>
                    <p style="color: #6b7280; margin-bottom: 24px;">基于LSTM深度学习模型分析历史价格、成交量、技术指标等多维度数据</p>
                </div>
                <div class="prediction-grid">
                    ${currentPredictions.map(pred => `
                        <div class="prediction-card">
                            <div class="prediction-day">第${pred.day}天</div>
                            <div class="prediction-price">$${pred.price}</div>
                            <div class="prediction-change ${pred.change >= 0 ? 'positive' : 'negative'}">
                                ${pred.change >= 0 ? '+' : ''}${pred.change}%
                            </div>
                            <div style="font-size: 10px; color: #9ca3af; margin-top: 4px;">
                                置信度: ${(pred.confidence * 100).toFixed(1)}%
                            </div>
                        </div>
                    `).join('')}
                </div>
                <div style="margin-top: 32px; padding: 20px; background: #f0f9ff; border-radius: 12px; border: 1px solid #0ea5e9;">
                    <h5 style="color: #0369a1; margin-bottom: 12px;">🧠 AI分析摘要</h5>
                    <p style="color: #0c4a6e; line-height: 1.6;">
                        基于LSTM模型分析，预测${currentStock.symbol}在未来10个交易日内
                        ${currentPredictions[9].change >= 0 ? '整体呈上涨趋势' : '整体呈下跌趋势'}，
                        累计变化约${currentPredictions[9].change >= 0 ? '+' : ''}${currentPredictions[9].change.toFixed(2)}%。
                        模型置信度为${(currentPredictions[0].confidence * 100).toFixed(1)}%，
                        建议结合技术分析和基本面分析做出投资决策。
                    </p>
                </div>
            `;

            predictBtn.disabled = false;
            predictBtn.textContent = '重新预测';

            // 生成投资建议
            const advice = generateAdvice();
            displayAdvice(advice);
        }

        // 显示投资建议
        function displayAdvice(advice) {
            document.getElementById('adviceContent').innerHTML = `
                <div class="advice-card ${advice.type}">
                    <div class="advice-title ${advice.type}">${advice.recommendation}</div>
                    <div style="font-size: 18px; color: #6b7280; margin-bottom: 16px;">
                        综合评分: ${advice.score}/5 | 置信度: ${(advice.confidence * 100).toFixed(1)}%
                    </div>
                    <div class="advice-details">
                        <div>
                            <div style="color: #6b7280; font-size: 14px;">目标价位</div>
                            <div style="font-weight: bold; font-size: 18px; color: #111827;">$${advice.targetPrice}</div>
                        </div>
                        <div>
                            <div style="color: #6b7280; font-size: 14px;">止损价位</div>
                            <div style="font-weight: bold; font-size: 18px; color: #111827;">$${advice.stopLoss}</div>
                        </div>
                        <div>
                            <div style="color: #6b7280; font-size: 14px;">潜在收益</div>
                            <div style="font-weight: bold; font-size: 18px; color: ${advice.type === 'buy' ? '#059669' : advice.type === 'sell' ? '#dc2626' : '#6b7280'};">
                                ${((advice.targetPrice - currentStock.price) / currentStock.price * 100).toFixed(2)}%
                            </div>
                        </div>
                    </div>
                </div>
                <div style="margin-top: 24px;">
                    <h4 style="color: #667eea; margin-bottom: 16px;">📊 分析依据</h4>
                    <div style="background: #f8fafc; padding: 20px; border-radius: 12px; border: 1px solid #e2e8f0;">
                        ${advice.signals.map(signal => `
                            <div style="margin-bottom: 8px; display: flex; align-items: center;">
                                <div style="width: 8px; height: 8px; background: #667eea; border-radius: 50%; margin-right: 12px;"></div>
                                <span style="color: #374151;">${signal}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div style="margin-top: 24px; padding: 16px; background: #fef3c7; border-radius: 12px; border: 1px solid #f59e0b;">
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <span style="font-size: 20px; margin-right: 8px;">⚠️</span>
                        <span style="font-weight: bold; color: #92400e;">风险提示</span>
                    </div>
                    <p style="color: #92400e; font-size: 14px; line-height: 1.5;">
                        本分析仅供参考，不构成投资建议。股票投资存在风险，过往表现不代表未来收益。
                        请根据自身风险承受能力和投资目标做出决策，建议咨询专业投资顾问。
                    </p>
                </div>
            `;
        }

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content > div').forEach(tab => {
                tab.classList.add('hidden');
            });

            // 移除所有标签页的active状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页
            document.getElementById(tabName + 'Tab').classList.remove('hidden');

            // 设置对应按钮为active
            event.target.classList.add('active');
        }

        // 搜索股票
        function searchStock(symbol) {
            document.getElementById('searchInput').value = symbol;
            selectStock(symbol);
        }

        // 事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            const searchBtn = document.getElementById('searchBtn');

            searchInput.addEventListener('input', function() {
                const query = this.value.trim();
                if (query.length >= 1) {
                    const results = searchStocks(query);
                    showSuggestions(results);
                } else {
                    document.getElementById('suggestions').classList.add('hidden');
                }
            });

            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const query = this.value.trim().toUpperCase();
                    if (query) {
                        selectStock(query);
                    }
                }
            });

            searchBtn.addEventListener('click', function() {
                const query = searchInput.value.trim().toUpperCase();
                if (query) {
                    selectStock(query);
                }
            });

            // 点击外部关闭建议
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.search-box')) {
                    document.getElementById('suggestions').classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>
