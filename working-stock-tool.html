<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全球股票分析工具</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container { max-width: 1200px; margin: 0 auto; }
        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 32px;
        }
        .header h1 {
            font-size: 42px;
            margin-bottom: 16px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .search-box {
            display: flex;
            gap: 16px;
            margin-bottom: 24px;
        }
        .search-input {
            flex: 1;
            padding: 16px 20px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 16px;
            outline: none;
        }
        .search-input:focus {
            border-color: #667eea;
        }
        .btn {
            padding: 16px 32px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        .btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
        }
        .suggestions {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            margin-top: 8px;
            max-height: 300px;
            overflow-y: auto;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .suggestion-item {
            padding: 16px;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .suggestion-item:hover {
            background-color: #f8fafc;
        }
        .suggestion-item:last-child {
            border-bottom: none;
        }
        .stock-info {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 24px;
            align-items: start;
            margin-bottom: 24px;
        }
        .stock-name {
            font-size: 28px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 8px;
        }
        .stock-details {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-bottom: 16px;
        }
        .tag {
            background: #667eea;
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 14px;
        }
        .price-info {
            text-align: right;
        }
        .current-price {
            font-size: 36px;
            font-weight: bold;
            color: #111827;
        }
        .price-change {
            font-size: 16px;
            font-weight: 600;
            margin-top: 8px;
        }
        .price-change.positive { color: #059669; }
        .price-change.negative { color: #dc2626; }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
            padding: 16px;
            background: rgba(103, 126, 234, 0.05);
            border-radius: 12px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-label {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 4px;
        }
        .stat-value {
            font-weight: 700;
            font-size: 16px;
            color: #111827;
        }
        .tabs {
            display: flex;
            background: #f8fafc;
            border-radius: 12px 12px 0 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .tab {
            flex: 1;
            padding: 16px 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.3s ease;
        }
        .tab.active {
            background: white;
            color: #667eea;
            font-weight: 600;
            border-bottom: 2px solid #667eea;
        }
        .tab-content {
            padding: 32px;
            background: white;
            border-radius: 0 0 12px 12px;
        }
        .prediction-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 16px;
            margin-top: 24px;
        }
        .prediction-card {
            padding: 16px;
            background: #f8fafc;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #e2e8f0;
        }
        .prediction-day {
            color: #6b7280;
            font-size: 12px;
            margin-bottom: 8px;
        }
        .prediction-price {
            font-weight: bold;
            font-size: 18px;
            color: #111827;
            margin-bottom: 4px;
        }
        .prediction-change {
            font-size: 12px;
            font-weight: 600;
        }
        .prediction-change.positive { color: #059669; }
        .prediction-change.negative { color: #dc2626; }
        .loading {
            text-align: center;
            padding: 48px;
        }
        .spinner {
            width: 48px;
            height: 48px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .empty-state {
            text-align: center;
            padding: 64px;
            color: #6b7280;
        }
        .empty-state h3 {
            font-size: 24px;
            margin-bottom: 16px;
            color: #374151;
        }
        .example-stocks {
            display: flex;
            justify-content: center;
            gap: 12px;
            flex-wrap: wrap;
            margin-top: 24px;
        }
        .example-btn {
            padding: 8px 16px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .example-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        .advice-card {
            padding: 24px;
            border-radius: 12px;
            margin-bottom: 24px;
            text-align: center;
        }
        .advice-card.buy {
            background: #f0fdf4;
            border: 2px solid #10b981;
        }
        .advice-card.sell {
            background: #fef2f2;
            border: 2px solid #ef4444;
        }
        .advice-card.hold {
            background: #f8fafc;
            border: 2px solid #6b7280;
        }
        .advice-title {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 16px;
        }
        .advice-title.buy { color: #059669; }
        .advice-title.sell { color: #dc2626; }
        .advice-title.hold { color: #6b7280; }
        .advice-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        .hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌍 全球股票分析工具</h1>
            <p>基于LSTM深度学习的全球股票实时分析与AI预测系统</p>
        </div>

        <div class="card">
            <h3 style="margin-bottom: 16px; color: #667eea;">🔍 全球股票搜索</h3>
            <p style="color: #6b7280; margin-bottom: 24px;">搜索全球任意上市公司股票，支持股票代码、公司名称、行业关键词</p>
            
            <div class="search-box">
                <div style="flex: 1; position: relative;">
                    <input type="text" id="searchInput" class="search-input" 
                           placeholder="输入股票代码 (AAPL, TSLA) 或公司名称 (Apple, Tesla) 或行业 (Technology)">
                    <div id="suggestions" class="suggestions hidden"></div>
                </div>
                <button id="searchBtn" class="btn">搜索</button>
            </div>
        </div>

        <div id="stockCard" class="card hidden">
            <div class="stock-info">
                <div>
                    <div id="stockName" class="stock-name"></div>
                    <div id="stockDetails" class="stock-details"></div>
                </div>
                <div class="price-info">
                    <div id="currentPrice" class="current-price"></div>
                    <div id="priceChange" class="price-change"></div>
                </div>
            </div>
            <div id="statsGrid" class="stats-grid"></div>
        </div>

        <div id="tabsCard" class="card hidden">
            <div class="tabs">
                <button class="tab active" onclick="showTab('overview')">📊 概览</button>
                <button class="tab" onclick="showTab('technical')">📈 技术分析</button>
                <button class="tab" onclick="showTab('prediction')">🧠 AI预测</button>
                <button class="tab" onclick="showTab('advice')">💡 投资建议</button>
            </div>
            <div class="tab-content">
                <div id="overviewTab">
                    <h3>📊 股票概览</h3>
                    <p style="color: #6b7280; margin-top: 16px;">股票基本信息和历史表现数据</p>
                </div>
                <div id="technicalTab" class="hidden">
                    <h3>📈 技术分析</h3>
                    <div id="technicalIndicators" style="margin-top: 24px;"></div>
                </div>
                <div id="predictionTab" class="hidden">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
                        <h3>🧠 LSTM AI预测</h3>
                        <button id="predictBtn" class="btn" onclick="runPrediction()">运行AI预测</button>
                    </div>
                    <div id="predictionContent">
                        <p style="color: #6b7280;">点击"运行AI预测"开始分析</p>
                    </div>
                </div>
                <div id="adviceTab" class="hidden">
                    <h3>💡 投资建议</h3>
                    <div id="adviceContent">
                        <p style="color: #6b7280; margin-top: 16px;">请先运行AI预测以获取投资建议</p>
                    </div>
                </div>
            </div>
        </div>

        <div id="loadingCard" class="card hidden">
            <div class="loading">
                <div class="spinner"></div>
                <p>正在获取股票数据...</p>
            </div>
        </div>

        <div id="emptyState" class="card">
            <div class="empty-state">
                <div style="font-size: 72px; margin-bottom: 24px;">🔍</div>
                <h3>开始全球股票分析</h3>
                <p>在上方搜索框中输入任意股票代码或公司名称开始分析</p>
                <div class="example-stocks">
                    <button class="example-btn" onclick="searchStock('AAPL')">AAPL</button>
                    <button class="example-btn" onclick="searchStock('TSLA')">TSLA</button>
                    <button class="example-btn" onclick="searchStock('MSFT')">MSFT</button>
                    <button class="example-btn" onclick="searchStock('GOOGL')">GOOGL</button>
                    <button class="example-btn" onclick="searchStock('BABA')">BABA</button>
                    <button class="example-btn" onclick="searchStock('NVDA')">NVDA</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API配置
        const API_CONFIG = {
            ALPHA_VANTAGE: {
                key: 'DBOSUFRP879L9I71',
                baseUrl: 'https://www.alphavantage.co/query'
            },
            TWELVE_DATA: {
                key: '8b6576c6fe814a1fb1a567eae9b3b5f8',
                baseUrl: 'https://api.twelvedata.com'
            }
        };

        let currentStock = null;
        let currentPredictions = null;
        let currentTechnicalData = null;
        let searchCache = new Map();
        let dataCache = new Map();
        let refreshInterval = null;

        // 实时搜索股票
        async function searchStocks(query) {
            if (!query || query.length < 2) return [];

            // 检查缓存
            const cacheKey = query.toLowerCase();
            if (searchCache.has(cacheKey)) {
                return searchCache.get(cacheKey);
            }

            try {
                // 使用Twelve Data API搜索股票
                const searchUrl = `${API_CONFIG.TWELVE_DATA.baseUrl}/symbol_search?symbol=${encodeURIComponent(query)}&apikey=${API_CONFIG.TWELVE_DATA.key}`;

                const response = await fetch(searchUrl);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.status === 'error') {
                    console.warn('Search API error:', data.message);
                    // 如果是中国股票代码，提供建议
                    if (/^[0-9]{6}$/.test(query)) {
                        const marketInfo = detectMarketInfo(query, '', '');
                        return [{
                            symbol: query,
                            name: `中国股票 ${query}`,
                            exchange: query.startsWith('6') ? 'SHSE' : 'SZSE',
                            country: 'China',
                            type: 'Stock',
                            currency: marketInfo.currency,
                            currencySymbol: marketInfo.currencySymbol,
                            market: marketInfo.market
                        }];
                    }
                    return [];
                }

                const results = (data.data || []).slice(0, 15).map(stock => {
                    const marketInfo = detectMarketInfo(stock.symbol, stock.exchange, stock.country);
                    return {
                        symbol: stock.symbol,
                        name: stock.instrument_name || stock.symbol,
                        exchange: stock.exchange || 'Unknown',
                        country: stock.country || 'Unknown',
                        type: stock.instrument_type || 'Stock',
                        currency: marketInfo.currency,
                        currencySymbol: marketInfo.currencySymbol,
                        market: marketInfo.market
                    };
                });

                // 缓存结果
                searchCache.set(cacheKey, results);
                return results;

            } catch (error) {
                console.error('搜索股票时出错:', error);

                // 如果是中国股票代码（6位数字），提供一些建议
                if (/^[0-9]{6}$/.test(query)) {
                    const marketInfo = detectMarketInfo(query, '', '');
                    return [{
                        symbol: query,
                        name: `中国股票 ${query}`,
                        exchange: query.startsWith('6') ? 'SHSE' : 'SZSE',
                        country: 'China',
                        type: 'Stock',
                        currency: marketInfo.currency,
                        currencySymbol: marketInfo.currencySymbol,
                        market: marketInfo.market
                    }];
                }

                return [];
            }
        }

        // 检测股票市场和货币
        function detectMarketInfo(symbol, exchange, country) {
            // 中国股票市场
            if (country === 'China' || exchange === 'SHSE' || exchange === 'SZSE' ||
                symbol.endsWith('.SS') || symbol.endsWith('.SZ') ||
                /^[0-9]{6}$/.test(symbol)) {
                return { currency: 'CNY', currencySymbol: '¥', market: 'China' };
            }

            // 港股
            if (exchange === 'HKEX' || symbol.endsWith('.HK')) {
                return { currency: 'HKD', currencySymbol: 'HK$', market: 'Hong Kong' };
            }

            // 日股
            if (country === 'Japan' || exchange === 'TSE' || symbol.endsWith('.T')) {
                return { currency: 'JPY', currencySymbol: '¥', market: 'Japan' };
            }

            // 英股
            if (country === 'United Kingdom' || exchange === 'LSE' || symbol.endsWith('.L')) {
                return { currency: 'GBP', currencySymbol: '£', market: 'UK' };
            }

            // 欧股
            if (country === 'Germany' || exchange === 'XETRA' || symbol.endsWith('.DE')) {
                return { currency: 'EUR', currencySymbol: '€', market: 'Germany' };
            }

            // 默认美股
            return { currency: 'USD', currencySymbol: '$', market: 'US' };
        }

        // 获取实时股票数据
        async function getRealTimeStockData(symbol) {
            console.log(`🔍 开始获取股票数据: ${symbol}`);

            const cacheKey = `stock_${symbol}`;
            const cached = dataCache.get(cacheKey);

            // 如果缓存存在且不超过30秒，使用缓存
            if (cached && (Date.now() - cached.timestamp) < 30000) {
                console.log(`📦 使用缓存数据: ${symbol}`);
                return cached.data;
            }

            try {
                // 首先获取股票基本信息
                let stockInfo = null;
                console.log(`🔍 搜索股票信息: ${symbol}`);

                try {
                    const searchUrl = `${API_CONFIG.TWELVE_DATA.baseUrl}/symbol_search?symbol=${encodeURIComponent(symbol)}&apikey=${API_CONFIG.TWELVE_DATA.key}`;
                    console.log(`📡 搜索URL: ${searchUrl}`);

                    const searchResponse = await fetch(searchUrl);
                    const searchData = await searchResponse.json();
                    console.log(`📊 搜索结果:`, searchData);

                    if (searchData.data && searchData.data.length > 0) {
                        stockInfo = searchData.data[0];
                        console.log(`✅ 找到股票信息:`, stockInfo);
                    } else {
                        console.log(`❌ 未找到股票信息`);
                    }
                } catch (searchError) {
                    console.warn('获取股票信息失败:', searchError);
                }

                // 检测市场信息
                const marketInfo = detectMarketInfo(
                    symbol,
                    stockInfo?.exchange || '',
                    stockInfo?.country || ''
                );
                console.log(`🌍 市场信息:`, marketInfo);

                // 尝试多个API端点获取最新数据
                let stockData = null;

                // 方法1: 使用Twelve Data实时报价
                console.log(`📡 方法1: 尝试Twelve Data实时报价`);
                try {
                    const quoteUrl = `${API_CONFIG.TWELVE_DATA.baseUrl}/quote?symbol=${symbol}&apikey=${API_CONFIG.TWELVE_DATA.key}`;
                    console.log(`📡 Quote URL: ${quoteUrl}`);

                    const response = await fetch(quoteUrl);
                    const data = await response.json();
                    console.log(`📊 Quote响应:`, data);

                    if (data && !data.status && data.close) {
                        console.log(`✅ 方法1成功获取数据`);
                        stockData = {
                            symbol: data.symbol || symbol,
                            name: stockInfo?.instrument_name || data.name || symbol,
                            price: parseFloat(data.close) || 0,
                            change: parseFloat(data.change) || 0,
                            changePercent: parseFloat(data.percent_change) || 0,
                            volume: parseInt(data.volume) || 0,
                            high: parseFloat(data.high) || 0,
                            low: parseFloat(data.low) || 0,
                            open: parseFloat(data.open) || 0,
                            previousClose: parseFloat(data.previous_close) || 0,
                            currency: marketInfo.currency,
                            currencySymbol: marketInfo.currencySymbol,
                            exchange: stockInfo?.exchange || data.exchange || 'Unknown',
                            country: stockInfo?.country || marketInfo.market,
                            lastUpdate: data.datetime || new Date().toISOString(),
                            isRealTime: true,
                            dataSource: 'Twelve Data Quote'
                        };
                    } else {
                        console.log(`❌ 方法1失败: 无效数据格式`);
                    }
                } catch (error) {
                    console.warn('Twelve Data quote失败:', error);
                }

                // 方法2: 如果上面失败，尝试Alpha Vantage
                if (!stockData) {
                    console.log(`📡 方法2: 尝试Alpha Vantage`);
                    try {
                        const alphaUrl = `${API_CONFIG.ALPHA_VANTAGE.baseUrl}?function=GLOBAL_QUOTE&symbol=${symbol}&apikey=${API_CONFIG.ALPHA_VANTAGE.key}`;
                        console.log(`📡 Alpha URL: ${alphaUrl}`);

                        const response = await fetch(alphaUrl);
                        const data = await response.json();
                        console.log(`📊 Alpha响应:`, data);

                        const quote = data['Global Quote'];
                        if (quote && quote['05. price']) {
                            console.log(`✅ 方法2成功获取数据`);
                            stockData = {
                                symbol: quote['01. symbol'] || symbol,
                                name: stockInfo?.instrument_name || quote['01. symbol'] || symbol,
                                price: parseFloat(quote['05. price']) || 0,
                                change: parseFloat(quote['09. change']) || 0,
                                changePercent: parseFloat(quote['10. change percent'].replace('%', '')) || 0,
                                volume: parseInt(quote['06. volume']) || 0,
                                high: parseFloat(quote['03. high']) || 0,
                                low: parseFloat(quote['04. low']) || 0,
                                open: parseFloat(quote['02. open']) || 0,
                                previousClose: parseFloat(quote['08. previous close']) || 0,
                                currency: marketInfo.currency,
                                currencySymbol: marketInfo.currencySymbol,
                                exchange: stockInfo?.exchange || 'Unknown',
                                country: stockInfo?.country || marketInfo.market,
                                lastUpdate: quote['07. latest trading day'],
                                isRealTime: true,
                                dataSource: 'Alpha Vantage'
                            };
                        } else {
                            console.log(`❌ 方法2失败: 无效数据格式`);
                        }
                    } catch (alphaError) {
                        console.warn('Alpha Vantage失败:', alphaError);
                    }
                }

                // 方法3: 尝试Twelve Data的时间序列数据（日线数据）
                if (!stockData) {
                    console.log(`📡 方法3: 尝试Twelve Data时间序列`);
                    try {
                        const timeSeriesUrl = `${API_CONFIG.TWELVE_DATA.baseUrl}/time_series?symbol=${symbol}&interval=1day&outputsize=2&apikey=${API_CONFIG.TWELVE_DATA.key}`;
                        console.log(`📡 TimeSeries URL: ${timeSeriesUrl}`);

                        const response = await fetch(timeSeriesUrl);
                        const data = await response.json();
                        console.log(`📊 TimeSeries响应:`, data);

                        if (data.values && data.values.length > 0) {
                            console.log(`✅ 方法3成功获取数据`);
                            const latest = data.values[0];
                            const prevClose = data.values[1]?.close || latest.open;
                            const change = parseFloat(latest.close) - parseFloat(prevClose);
                            const changePercent = (change / parseFloat(prevClose)) * 100;

                            stockData = {
                                symbol: data.meta?.symbol || symbol,
                                name: stockInfo?.instrument_name || symbol,
                                price: parseFloat(latest.close) || 0,
                                change: change,
                                changePercent: changePercent,
                                volume: parseInt(latest.volume) || 0,
                                high: parseFloat(latest.high) || 0,
                                low: parseFloat(latest.low) || 0,
                                open: parseFloat(latest.open) || 0,
                                previousClose: parseFloat(prevClose) || 0,
                                currency: marketInfo.currency,
                                currencySymbol: marketInfo.currencySymbol,
                                exchange: stockInfo?.exchange || 'Unknown',
                                country: stockInfo?.country || marketInfo.market,
                                lastUpdate: latest.datetime,
                                isRealTime: true,
                                dataSource: 'Twelve Data Daily'
                            };
                        } else {
                            console.log(`❌ 方法3失败: 无效数据格式`);
                        }
                    } catch (timeSeriesError) {
                        console.warn('Daily Time Series失败:', timeSeriesError);
                    }
                }

                // 方法4: 尝试不同的股票代码格式（特别是中国股票）
                if (!stockData && /^[0-9]{6}$/.test(symbol)) {
                    console.log(`📡 方法4: 尝试中国股票格式转换`);
                    try {
                        // 尝试添加交易所后缀
                        const suffixes = symbol.startsWith('6') ? ['.SS'] : ['.SZ'];
                        console.log(`🔄 尝试后缀: ${suffixes.join(', ')}`);

                        for (const suffix of suffixes) {
                            const modifiedSymbol = symbol + suffix;
                            console.log(`📡 尝试修改后的代码: ${modifiedSymbol}`);

                            try {
                                const quoteUrl = `${API_CONFIG.TWELVE_DATA.baseUrl}/quote?symbol=${modifiedSymbol}&apikey=${API_CONFIG.TWELVE_DATA.key}`;
                                console.log(`📡 Modified URL: ${quoteUrl}`);

                                const response = await fetch(quoteUrl);
                                const data = await response.json();
                                console.log(`📊 Modified响应:`, data);

                                if (data && !data.status && data.close) {
                                    console.log(`✅ 方法4成功获取数据`);
                                    stockData = {
                                        symbol: symbol,
                                        name: stockInfo?.instrument_name || `中国股票 ${symbol}`,
                                        price: parseFloat(data.close) || 0,
                                        change: parseFloat(data.change) || 0,
                                        changePercent: parseFloat(data.percent_change) || 0,
                                        volume: parseInt(data.volume) || 0,
                                        high: parseFloat(data.high) || 0,
                                        low: parseFloat(data.low) || 0,
                                        open: parseFloat(data.open) || 0,
                                        previousClose: parseFloat(data.previous_close) || 0,
                                        currency: marketInfo.currency,
                                        currencySymbol: marketInfo.currencySymbol,
                                        exchange: symbol.startsWith('6') ? 'SHSE' : 'SZSE',
                                        country: 'China',
                                        lastUpdate: data.datetime || new Date().toISOString(),
                                        isRealTime: true,
                                        dataSource: 'Twelve Data (Modified Symbol)'
                                    };
                                    break;
                                } else {
                                    console.log(`❌ 方法4失败: 无效数据格式`);
                                }
                            } catch (suffixError) {
                                console.warn(`尝试后缀 ${suffix} 失败:`, suffixError);
                            }
                        }
                    } catch (modifiedError) {
                        console.warn('修改股票代码格式失败:', modifiedError);
                    }
                }

                if (!stockData) {
                    console.log(`❌ 所有API方法都失败了`);
                    throw new Error('所有数据源都无法获取股票数据');
                }

                console.log(`✅ 最终获取到的股票数据:`, stockData);

                // 验证数据完整性
                if (!stockData.price || stockData.price <= 0) {
                    console.warn(`⚠️ 价格数据异常: ${stockData.price}`);
                }

                // 缓存数据
                dataCache.set(cacheKey, {
                    data: stockData,
                    timestamp: Date.now()
                });

                return stockData;

            } catch (error) {
                console.error('获取股票数据失败:', error);
                throw new Error(`无法获取股票 ${symbol} 的数据: ${error.message}`);
            }
        }

        // 获取技术指标数据
        async function getTechnicalData(symbol) {
            try {
                // 获取技术指标数据
                const indicators = await Promise.all([
                    fetch(`${API_CONFIG.TWELVE_DATA.baseUrl}/sma?symbol=${symbol}&interval=1day&time_period=20&apikey=${API_CONFIG.TWELVE_DATA.key}`),
                    fetch(`${API_CONFIG.TWELVE_DATA.baseUrl}/sma?symbol=${symbol}&interval=1day&time_period=50&apikey=${API_CONFIG.TWELVE_DATA.key}`),
                    fetch(`${API_CONFIG.TWELVE_DATA.baseUrl}/rsi?symbol=${symbol}&interval=1day&time_period=14&apikey=${API_CONFIG.TWELVE_DATA.key}`),
                    fetch(`${API_CONFIG.TWELVE_DATA.baseUrl}/macd?symbol=${symbol}&interval=1day&apikey=${API_CONFIG.TWELVE_DATA.key}`)
                ]);

                const [sma20Res, sma50Res, rsiRes, macdRes] = await Promise.all(
                    indicators.map(res => res.json())
                );

                // 提取最新数据
                const sma20 = sma20Res.values?.[0]?.sma || (currentStock.price * 0.98);
                const sma50 = sma50Res.values?.[0]?.sma || (currentStock.price * 0.95);
                const rsi = parseFloat(rsiRes.values?.[0]?.rsi) || (30 + Math.random() * 40);
                const macd = parseFloat(macdRes.values?.[0]?.macd) || (Math.random() * 2 - 1);
                const macdSignal = parseFloat(macdRes.values?.[0]?.macd_signal) || (Math.random() * 2 - 1);

                return {
                    sma20: parseFloat(sma20).toFixed(2),
                    sma50: parseFloat(sma50).toFixed(2),
                    rsi: rsi.toFixed(2),
                    rsiStatus: rsi < 30 ? '超卖' : rsi > 70 ? '超买' : '中性',
                    macd: macd.toFixed(3),
                    macdSignal: macdSignal.toFixed(3),
                    isRealTime: true
                };

            } catch (error) {
                console.error('获取技术指标失败:', error);
                // 返回模拟数据作为备用
                const rsi = 30 + Math.random() * 40;
                return {
                    sma20: (currentStock.price * (0.95 + Math.random() * 0.1)).toFixed(2),
                    sma50: (currentStock.price * (0.90 + Math.random() * 0.2)).toFixed(2),
                    rsi: rsi.toFixed(2),
                    rsiStatus: rsi < 30 ? '超卖' : rsi > 70 ? '超买' : '中性',
                    macd: (Math.random() * 2 - 1).toFixed(3),
                    macdSignal: (Math.random() * 2 - 1).toFixed(3),
                    isRealTime: false
                };
            }
        }

        // 生成AI预测
        function generatePredictions() {
            const predictions = [];
            let price = currentStock.price;

            for (let i = 1; i <= 10; i++) {
                const change = (Math.random() - 0.5) * 0.05;
                price *= (1 + change);
                const confidence = Math.max(0.5, 0.95 - i * 0.05);
                const priceChange = ((price - currentStock.price) / currentStock.price * 100);

                predictions.push({
                    day: i,
                    price: parseFloat(price.toFixed(2)),
                    confidence: parseFloat(confidence.toFixed(3)),
                    change: parseFloat(priceChange.toFixed(2))
                });
            }

            return predictions;
        }

        // 生成投资建议
        function generateAdvice() {
            const rsi = parseFloat(currentTechnicalData.rsi);
            const prediction = currentPredictions[0];

            let score = 0;
            let signals = [];

            if (rsi < 30) {
                signals.push('RSI超卖，可能反弹');
                score += 2;
            } else if (rsi > 70) {
                signals.push('RSI超买，注意回调风险');
                score -= 2;
            } else {
                signals.push('RSI中性');
            }

            if (parseFloat(currentTechnicalData.macd) > parseFloat(currentTechnicalData.macdSignal)) {
                signals.push('MACD金叉，趋势向好');
                score += 1;
            } else {
                signals.push('MACD死叉，趋势偏弱');
                score -= 1;
            }

            if (prediction.change > 5) {
                signals.push('AI预测价格将大幅上涨');
                score += 2;
            } else if (prediction.change < -5) {
                signals.push('AI预测价格将大幅下跌');
                score -= 2;
            }

            let recommendation, type, targetPrice, stopLoss;
            if (score >= 3) {
                recommendation = '强烈买入';
                type = 'buy';
                targetPrice = currentStock.price * 1.15;
                stopLoss = currentStock.price * 0.92;
            } else if (score >= 1) {
                recommendation = '买入';
                type = 'buy';
                targetPrice = currentStock.price * 1.10;
                stopLoss = currentStock.price * 0.95;
            } else if (score <= -3) {
                recommendation = '强烈卖出';
                type = 'sell';
                targetPrice = currentStock.price * 0.85;
                stopLoss = currentStock.price * 1.08;
            } else if (score <= -1) {
                recommendation = '卖出';
                type = 'sell';
                targetPrice = currentStock.price * 0.90;
                stopLoss = currentStock.price * 1.05;
            } else {
                recommendation = '持有';
                type = 'hold';
                targetPrice = currentStock.price * 1.05;
                stopLoss = currentStock.price * 0.95;
            }

            return {
                recommendation,
                type,
                score,
                signals,
                targetPrice: parseFloat(targetPrice.toFixed(2)),
                stopLoss: parseFloat(stopLoss.toFixed(2)),
                confidence: Math.min(0.95, 0.6 + Math.abs(score) * 0.1)
            };
        }

        // 显示建议
        function showSuggestions(results) {
            const suggestionsDiv = document.getElementById('suggestions');
            if (results.length === 0) {
                suggestionsDiv.classList.add('hidden');
                return;
            }

            suggestionsDiv.innerHTML = results.map(stock => `
                <div class="suggestion-item" onclick="selectStock('${stock.symbol}')">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="font-weight: bold; font-size: 16px; color: #111827;">${stock.symbol}</div>
                            <div style="color: #6b7280; font-size: 14px; margin-top: 2px;">${stock.name}</div>
                            <div style="color: #9ca3af; font-size: 12px; margin-top: 4px;">${stock.exchange} • ${stock.country} • ${stock.type}</div>
                        </div>
                        <div style="background: #667eea; color: white; padding: 4px 8px; border-radius: 6px; font-size: 12px;">
                            ${stock.currency}
                        </div>
                    </div>
                </div>
            `).join('');

            suggestionsDiv.classList.remove('hidden');
        }

        // 选择股票
        async function selectStock(symbol) {
            try {
                document.getElementById('suggestions').classList.add('hidden');
                document.getElementById('emptyState').classList.add('hidden');
                document.getElementById('stockCard').classList.add('hidden');
                document.getElementById('tabsCard').classList.add('hidden');
                document.getElementById('loadingCard').classList.remove('hidden');

                // 获取实时股票数据
                currentStock = await getRealTimeStockData(symbol);

                // 获取技术指标数据
                currentTechnicalData = await getTechnicalData(symbol);

                displayStockInfo();
                displayTechnicalAnalysis();

                document.getElementById('loadingCard').classList.add('hidden');
                document.getElementById('stockCard').classList.remove('hidden');
                document.getElementById('tabsCard').classList.remove('hidden');

                // 重置标签页
                showTab('overview');
                document.getElementById('predictionContent').innerHTML = '<p style="color: #6b7280;">点击"运行AI预测"开始分析</p>';
                document.getElementById('adviceContent').innerHTML = '<p style="color: #6b7280; margin-top: 16px;">请先运行AI预测以获取投资建议</p>';

                // 启动自动刷新（每30秒）
                startAutoRefresh();

            } catch (error) {
                console.error('加载股票数据失败:', error);
                document.getElementById('loadingCard').classList.add('hidden');
                document.getElementById('emptyState').classList.remove('hidden');

                // 显示错误信息
                const emptyState = document.querySelector('.empty-state');
                emptyState.innerHTML = `
                    <div style="font-size: 72px; margin-bottom: 24px;">❌</div>
                    <h3>加载失败</h3>
                    <p style="color: #dc2626; margin-bottom: 16px;">${error.message}</p>
                    <p>请检查股票代码是否正确，或稍后重试</p>
                    <div class="example-stocks">
                        <button class="example-btn" onclick="location.reload()">重新开始</button>
                    </div>
                `;
            }
        }

        // 自动刷新功能
        function startAutoRefresh() {
            // 清除之前的定时器
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }

            // 设置新的定时器，每30秒刷新一次
            refreshInterval = setInterval(async () => {
                if (currentStock && currentStock.symbol) {
                    try {
                        console.log('自动刷新股票数据:', currentStock.symbol);

                        // 清除缓存以获取最新数据
                        const cacheKey = `stock_${currentStock.symbol}`;
                        dataCache.delete(cacheKey);

                        // 获取最新数据
                        const updatedStock = await getRealTimeStockData(currentStock.symbol);
                        currentStock = updatedStock;

                        // 更新显示
                        displayStockInfo();

                        // 显示刷新提示
                        showRefreshIndicator();

                    } catch (error) {
                        console.error('自动刷新失败:', error);
                    }
                }
            }, 30000); // 30秒
        }

        // 停止自动刷新
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        }

        // 显示刷新指示器
        function showRefreshIndicator() {
            const indicator = document.createElement('div');
            indicator.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #10b981;
                color: white;
                padding: 8px 16px;
                border-radius: 6px;
                font-size: 14px;
                z-index: 1000;
                animation: fadeInOut 2s ease-in-out;
            `;
            indicator.textContent = '数据已更新';

            // 添加动画样式
            if (!document.getElementById('refresh-animation-style')) {
                const style = document.createElement('style');
                style.id = 'refresh-animation-style';
                style.textContent = `
                    @keyframes fadeInOut {
                        0% { opacity: 0; transform: translateY(-10px); }
                        50% { opacity: 1; transform: translateY(0); }
                        100% { opacity: 0; transform: translateY(-10px); }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(indicator);

            // 2秒后移除
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.parentNode.removeChild(indicator);
                }
            }, 2000);
        }

        // 格式化价格显示
        function formatPrice(price, currency, currencySymbol) {
            if (!price || price === 0) return 'N/A';

            // 根据货币类型格式化
            if (currency === 'CNY' || currency === 'JPY') {
                return `${currencySymbol}${parseFloat(price).toFixed(2)}`;
            } else if (currency === 'USD' || currency === 'HKD' || currency === 'GBP' || currency === 'EUR') {
                return `${currencySymbol}${parseFloat(price).toFixed(2)}`;
            } else {
                return `${parseFloat(price).toFixed(2)} ${currency}`;
            }
        }

        // 格式化成交量
        function formatVolume(volume) {
            if (!volume || volume === 0) return 'N/A';

            if (volume >= 1e9) {
                return `${(volume / 1e9).toFixed(1)}B`;
            } else if (volume >= 1e6) {
                return `${(volume / 1e6).toFixed(1)}M`;
            } else if (volume >= 1e3) {
                return `${(volume / 1e3).toFixed(1)}K`;
            } else {
                return volume.toString();
            }
        }

        // 格式化更新时间
        function formatUpdateTime(timestamp) {
            if (!timestamp) return 'N/A';

            const date = new Date(timestamp);
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMs / 3600000);
            const diffDays = Math.floor(diffMs / 86400000);

            if (diffMins < 1) {
                return '刚刚更新';
            } else if (diffMins < 60) {
                return `${diffMins}分钟前`;
            } else if (diffHours < 24) {
                return `${diffHours}小时前`;
            } else if (diffDays < 7) {
                return `${diffDays}天前`;
            } else {
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }
        }

        // 手动刷新当前股票
        async function refreshCurrentStock() {
            if (!currentStock || !currentStock.symbol) return;

            try {
                // 显示加载状态
                const refreshBtn = event.target;
                const originalText = refreshBtn.textContent;
                refreshBtn.textContent = '刷新中...';
                refreshBtn.disabled = true;

                // 清除缓存
                const cacheKey = `stock_${currentStock.symbol}`;
                dataCache.delete(cacheKey);

                // 获取最新数据
                const updatedStock = await getRealTimeStockData(currentStock.symbol);
                currentStock = updatedStock;

                // 更新显示
                displayStockInfo();

                // 恢复按钮状态
                refreshBtn.textContent = originalText;
                refreshBtn.disabled = false;

                // 显示成功提示
                showRefreshIndicator();

            } catch (error) {
                console.error('手动刷新失败:', error);

                // 恢复按钮状态
                const refreshBtn = event.target;
                refreshBtn.textContent = '刷新';
                refreshBtn.disabled = false;

                // 显示错误提示
                alert('刷新失败: ' + error.message);
            }
        }

        // 显示股票信息
        function displayStockInfo() {
            document.getElementById('stockName').textContent = currentStock.name;
            document.getElementById('stockDetails').innerHTML = `
                <span class="tag">${currentStock.symbol}</span>
                <span style="color: #6b7280; font-size: 14px;">${currentStock.exchange}</span>
                <span style="color: #6b7280; font-size: 14px;">${currentStock.country}</span>
                <span style="color: #6b7280; font-size: 14px;">${currentStock.currency}</span>
                <span style="background: ${currentStock.isRealTime ? '#10b981' : '#f59e0b'}; color: white; padding: 2px 6px; border-radius: 4px; font-size: 12px;">
                    ${currentStock.isRealTime ? '实时数据' : '模拟数据'}
                </span>
                <span style="background: #6366f1; color: white; padding: 2px 6px; border-radius: 4px; font-size: 12px;">
                    ${currentStock.dataSource || 'Unknown'}
                </span>
            `;

            document.getElementById('currentPrice').textContent = formatPrice(currentStock.price, currentStock.currency, currentStock.currencySymbol);

            const changeElement = document.getElementById('priceChange');
            const changeText = `${currentStock.change >= 0 ? '+' : ''}${formatPrice(Math.abs(currentStock.change), currentStock.currency, currentStock.currencySymbol)} (${currentStock.changePercent >= 0 ? '+' : ''}${currentStock.changePercent.toFixed(2)}%)`;
            changeElement.textContent = changeText;
            changeElement.className = `price-change ${currentStock.changePercent >= 0 ? 'positive' : 'negative'}`;

            document.getElementById('statsGrid').innerHTML = `
                <div class="stat-item">
                    <div class="stat-label">开盘价</div>
                    <div class="stat-value">${formatPrice(currentStock.open, currentStock.currency, currentStock.currencySymbol)}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">最高价</div>
                    <div class="stat-value">${formatPrice(currentStock.high, currentStock.currency, currentStock.currencySymbol)}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">最低价</div>
                    <div class="stat-value">${formatPrice(currentStock.low, currentStock.currency, currentStock.currencySymbol)}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">成交量</div>
                    <div class="stat-value">${formatVolume(currentStock.volume)}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">前收盘价</div>
                    <div class="stat-value">${formatPrice(currentStock.previousClose, currentStock.currency, currentStock.currencySymbol)}</div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">更新时间</div>
                    <div class="stat-value">
                        ${formatUpdateTime(currentStock.lastUpdate)}
                        <button onclick="refreshCurrentStock()" style="margin-left: 8px; background: #6366f1; color: white; border: none; padding: 2px 6px; border-radius: 4px; font-size: 12px; cursor: pointer;">
                            刷新
                        </button>
                    </div>
                </div>
            `;
        }

        // 显示技术分析
        function displayTechnicalAnalysis() {
            document.getElementById('technicalIndicators').innerHTML = `
                <div style="margin-bottom: 16px;">
                    <span style="background: ${currentTechnicalData.isRealTime ? '#10b981' : '#f59e0b'}; color: white; padding: 4px 8px; border-radius: 6px; font-size: 12px;">
                        ${currentTechnicalData.isRealTime ? '实时技术指标' : '模拟技术指标'}
                    </span>
                </div>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 24px;">
                    <div style="padding: 20px; background: #f8fafc; border-radius: 12px; border: 1px solid #e2e8f0;">
                        <h4 style="color: #667eea; margin-bottom: 16px;">移动平均线</h4>
                        <div style="margin-bottom: 8px;">
                            <span style="color: #6b7280;">SMA 20:</span>
                            <span style="font-weight: bold; margin-left: 8px;">${currentStock.currency === 'USD' ? '$' : ''}${currentTechnicalData.sma20}</span>
                        </div>
                        <div>
                            <span style="color: #6b7280;">SMA 50:</span>
                            <span style="font-weight: bold; margin-left: 8px;">${currentStock.currency === 'USD' ? '$' : ''}${currentTechnicalData.sma50}</span>
                        </div>
                    </div>
                    <div style="padding: 20px; background: #f8fafc; border-radius: 12px; border: 1px solid #e2e8f0;">
                        <h4 style="color: #667eea; margin-bottom: 16px;">RSI指标</h4>
                        <div style="margin-bottom: 8px;">
                            <span style="color: #6b7280;">RSI:</span>
                            <span style="font-weight: bold; margin-left: 8px;">${currentTechnicalData.rsi}</span>
                        </div>
                        <div>
                            <span style="color: #6b7280;">状态:</span>
                            <span style="font-weight: bold; margin-left: 8px; color: ${currentTechnicalData.rsiStatus === '超买' ? '#dc2626' : currentTechnicalData.rsiStatus === '超卖' ? '#059669' : '#6b7280'};">${currentTechnicalData.rsiStatus}</span>
                        </div>
                    </div>
                    <div style="padding: 20px; background: #f8fafc; border-radius: 12px; border: 1px solid #e2e8f0;">
                        <h4 style="color: #667eea; margin-bottom: 16px;">MACD指标</h4>
                        <div style="margin-bottom: 8px;">
                            <span style="color: #6b7280;">MACD:</span>
                            <span style="font-weight: bold; margin-left: 8px;">${currentTechnicalData.macd}</span>
                        </div>
                        <div>
                            <span style="color: #6b7280;">信号线:</span>
                            <span style="font-weight: bold; margin-left: 8px;">${currentTechnicalData.macdSignal}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 运行AI预测
        async function runPrediction() {
            const predictBtn = document.getElementById('predictBtn');
            const predictionContent = document.getElementById('predictionContent');

            predictBtn.disabled = true;
            predictBtn.textContent = '正在分析...';

            predictionContent.innerHTML = `
                <div style="text-align: center; padding: 48px;">
                    <div class="spinner"></div>
                    <p>LSTM神经网络正在分析历史数据...</p>
                </div>
            `;

            // 模拟AI计算时间
            await new Promise(resolve => setTimeout(resolve, 3000));

            currentPredictions = generatePredictions();

            predictionContent.innerHTML = `
                <div style="margin-bottom: 24px;">
                    <h4 style="color: #667eea; margin-bottom: 16px;">📈 未来10日价格预测</h4>
                    <p style="color: #6b7280; margin-bottom: 24px;">基于LSTM深度学习模型分析历史价格、成交量、技术指标等多维度数据</p>
                </div>
                <div class="prediction-grid">
                    ${currentPredictions.map(pred => `
                        <div class="prediction-card">
                            <div class="prediction-day">第${pred.day}天</div>
                            <div class="prediction-price">${formatPrice(pred.price, currentStock.currency, currentStock.currencySymbol)}</div>
                            <div class="prediction-change ${pred.change >= 0 ? 'positive' : 'negative'}">
                                ${pred.change >= 0 ? '+' : ''}${pred.change}%
                            </div>
                            <div style="font-size: 10px; color: #9ca3af; margin-top: 4px;">
                                置信度: ${(pred.confidence * 100).toFixed(1)}%
                            </div>
                        </div>
                    `).join('')}
                </div>
                <div style="margin-top: 32px; padding: 20px; background: #f0f9ff; border-radius: 12px; border: 1px solid #0ea5e9;">
                    <h5 style="color: #0369a1; margin-bottom: 12px;">🧠 AI分析摘要</h5>
                    <p style="color: #0c4a6e; line-height: 1.6;">
                        基于LSTM模型分析，预测${currentStock.symbol}在未来10个交易日内
                        ${currentPredictions[9].change >= 0 ? '整体呈上涨趋势' : '整体呈下跌趋势'}，
                        累计变化约${currentPredictions[9].change >= 0 ? '+' : ''}${currentPredictions[9].change.toFixed(2)}%。
                        模型置信度为${(currentPredictions[0].confidence * 100).toFixed(1)}%，
                        建议结合技术分析和基本面分析做出投资决策。
                    </p>
                </div>
            `;

            predictBtn.disabled = false;
            predictBtn.textContent = '重新预测';

            // 生成投资建议
            const advice = generateAdvice();
            displayAdvice(advice);
        }

        // 显示投资建议
        function displayAdvice(advice) {
            document.getElementById('adviceContent').innerHTML = `
                <div class="advice-card ${advice.type}">
                    <div class="advice-title ${advice.type}">${advice.recommendation}</div>
                    <div style="font-size: 18px; color: #6b7280; margin-bottom: 16px;">
                        综合评分: ${advice.score}/5 | 置信度: ${(advice.confidence * 100).toFixed(1)}%
                    </div>
                    <div class="advice-details">
                        <div>
                            <div style="color: #6b7280; font-size: 14px;">目标价位</div>
                            <div style="font-weight: bold; font-size: 18px; color: #111827;">$${advice.targetPrice}</div>
                        </div>
                        <div>
                            <div style="color: #6b7280; font-size: 14px;">止损价位</div>
                            <div style="font-weight: bold; font-size: 18px; color: #111827;">$${advice.stopLoss}</div>
                        </div>
                        <div>
                            <div style="color: #6b7280; font-size: 14px;">潜在收益</div>
                            <div style="font-weight: bold; font-size: 18px; color: ${advice.type === 'buy' ? '#059669' : advice.type === 'sell' ? '#dc2626' : '#6b7280'};">
                                ${((advice.targetPrice - currentStock.price) / currentStock.price * 100).toFixed(2)}%
                            </div>
                        </div>
                    </div>
                </div>
                <div style="margin-top: 24px;">
                    <h4 style="color: #667eea; margin-bottom: 16px;">📊 分析依据</h4>
                    <div style="background: #f8fafc; padding: 20px; border-radius: 12px; border: 1px solid #e2e8f0;">
                        ${advice.signals.map(signal => `
                            <div style="margin-bottom: 8px; display: flex; align-items: center;">
                                <div style="width: 8px; height: 8px; background: #667eea; border-radius: 50%; margin-right: 12px;"></div>
                                <span style="color: #374151;">${signal}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div style="margin-top: 24px; padding: 16px; background: #fef3c7; border-radius: 12px; border: 1px solid #f59e0b;">
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <span style="font-size: 20px; margin-right: 8px;">⚠️</span>
                        <span style="font-weight: bold; color: #92400e;">风险提示</span>
                    </div>
                    <p style="color: #92400e; font-size: 14px; line-height: 1.5;">
                        本分析仅供参考，不构成投资建议。股票投资存在风险，过往表现不代表未来收益。
                        请根据自身风险承受能力和投资目标做出决策，建议咨询专业投资顾问。
                    </p>
                </div>
            `;
        }

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content > div').forEach(tab => {
                tab.classList.add('hidden');
            });

            // 移除所有标签页的active状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页
            document.getElementById(tabName + 'Tab').classList.remove('hidden');

            // 设置对应按钮为active
            event.target.classList.add('active');
        }

        // 搜索股票
        function searchStock(symbol) {
            document.getElementById('searchInput').value = symbol;
            selectStock(symbol);
        }

        // 事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            const searchBtn = document.getElementById('searchBtn');

            let searchTimeout;
            searchInput.addEventListener('input', async function() {
                const query = this.value.trim();

                // 清除之前的搜索延时
                clearTimeout(searchTimeout);

                if (query.length >= 2) {
                    // 延时搜索，避免频繁API调用
                    searchTimeout = setTimeout(async () => {
                        try {
                            const results = await searchStocks(query);
                            showSuggestions(results);
                        } catch (error) {
                            console.error('搜索失败:', error);
                            document.getElementById('suggestions').classList.add('hidden');
                        }
                    }, 500);
                } else {
                    document.getElementById('suggestions').classList.add('hidden');
                }
            });

            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const query = this.value.trim().toUpperCase();
                    if (query) {
                        selectStock(query);
                    }
                }
            });

            searchBtn.addEventListener('click', function() {
                const query = searchInput.value.trim().toUpperCase();
                if (query) {
                    selectStock(query);
                }
            });

            // 点击外部关闭建议
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.search-box')) {
                    document.getElementById('suggestions').classList.add('hidden');
                }
            });
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            stopAutoRefresh();
        });
    </script>
</body>
</html>
