{"ast": null, "code": "import { timeInterval } from \"./interval.js\";\nimport { durationDay, durationMinute } from \"./duration.js\";\nexport const timeDay = timeInterval(date => date.setHours(0, 0, 0, 0), (date, step) => date.setDate(date.getDate() + step), (start, end) => (end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * durationMinute) / durationDay, date => date.getDate() - 1);\nexport const timeDays = timeDay.range;\nexport const utcDay = timeInterval(date => {\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCDate(date.getUTCDate() + step);\n}, (start, end) => {\n  return (end - start) / durationDay;\n}, date => {\n  return date.getUTCDate() - 1;\n});\nexport const utcDays = utcDay.range;\nexport const unixDay = timeInterval(date => {\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCDate(date.getUTCDate() + step);\n}, (start, end) => {\n  return (end - start) / durationDay;\n}, date => {\n  return Math.floor(date / durationDay);\n});\nexport const unixDays = unixDay.range;", "map": {"version": 3, "names": ["timeInterval", "durationDay", "durationMinute", "timeDay", "date", "setHours", "step", "setDate", "getDate", "start", "end", "getTimezoneOffset", "timeDays", "range", "utcDay", "setUTCHours", "setUTCDate", "getUTCDate", "utcDays", "unixDay", "Math", "floor", "unixDays"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/d3-time/src/day.js"], "sourcesContent": ["import {timeInterval} from \"./interval.js\";\nimport {durationDay, durationMinute} from \"./duration.js\";\n\nexport const timeDay = timeInterval(\n  date => date.setHours(0, 0, 0, 0),\n  (date, step) => date.setDate(date.getDate() + step),\n  (start, end) => (end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * durationMinute) / durationDay,\n  date => date.getDate() - 1\n);\n\nexport const timeDays = timeDay.range;\n\nexport const utcDay = timeInterval((date) => {\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCDate(date.getUTCDate() + step);\n}, (start, end) => {\n  return (end - start) / durationDay;\n}, (date) => {\n  return date.getUTCDate() - 1;\n});\n\nexport const utcDays = utcDay.range;\n\nexport const unixDay = timeInterval((date) => {\n  date.setUTCHours(0, 0, 0, 0);\n}, (date, step) => {\n  date.setUTCDate(date.getUTCDate() + step);\n}, (start, end) => {\n  return (end - start) / durationDay;\n}, (date) => {\n  return Math.floor(date / durationDay);\n});\n\nexport const unixDays = unixDay.range;\n"], "mappings": "AAAA,SAAQA,YAAY,QAAO,eAAe;AAC1C,SAAQC,WAAW,EAAEC,cAAc,QAAO,eAAe;AAEzD,OAAO,MAAMC,OAAO,GAAGH,YAAY,CACjCI,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACjC,CAACD,IAAI,EAAEE,IAAI,KAAKF,IAAI,CAACG,OAAO,CAACH,IAAI,CAACI,OAAO,CAAC,CAAC,GAAGF,IAAI,CAAC,EACnD,CAACG,KAAK,EAAEC,GAAG,KAAK,CAACA,GAAG,GAAGD,KAAK,GAAG,CAACC,GAAG,CAACC,iBAAiB,CAAC,CAAC,GAAGF,KAAK,CAACE,iBAAiB,CAAC,CAAC,IAAIT,cAAc,IAAID,WAAW,EACpHG,IAAI,IAAIA,IAAI,CAACI,OAAO,CAAC,CAAC,GAAG,CAC3B,CAAC;AAED,OAAO,MAAMI,QAAQ,GAAGT,OAAO,CAACU,KAAK;AAErC,OAAO,MAAMC,MAAM,GAAGd,YAAY,CAAEI,IAAI,IAAK;EAC3CA,IAAI,CAACW,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9B,CAAC,EAAE,CAACX,IAAI,EAAEE,IAAI,KAAK;EACjBF,IAAI,CAACY,UAAU,CAACZ,IAAI,CAACa,UAAU,CAAC,CAAC,GAAGX,IAAI,CAAC;AAC3C,CAAC,EAAE,CAACG,KAAK,EAAEC,GAAG,KAAK;EACjB,OAAO,CAACA,GAAG,GAAGD,KAAK,IAAIR,WAAW;AACpC,CAAC,EAAGG,IAAI,IAAK;EACX,OAAOA,IAAI,CAACa,UAAU,CAAC,CAAC,GAAG,CAAC;AAC9B,CAAC,CAAC;AAEF,OAAO,MAAMC,OAAO,GAAGJ,MAAM,CAACD,KAAK;AAEnC,OAAO,MAAMM,OAAO,GAAGnB,YAAY,CAAEI,IAAI,IAAK;EAC5CA,IAAI,CAACW,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9B,CAAC,EAAE,CAACX,IAAI,EAAEE,IAAI,KAAK;EACjBF,IAAI,CAACY,UAAU,CAACZ,IAAI,CAACa,UAAU,CAAC,CAAC,GAAGX,IAAI,CAAC;AAC3C,CAAC,EAAE,CAACG,KAAK,EAAEC,GAAG,KAAK;EACjB,OAAO,CAACA,GAAG,GAAGD,KAAK,IAAIR,WAAW;AACpC,CAAC,EAAGG,IAAI,IAAK;EACX,OAAOgB,IAAI,CAACC,KAAK,CAACjB,IAAI,GAAGH,WAAW,CAAC;AACvC,CAAC,CAAC;AAEF,OAAO,MAAMqB,QAAQ,GAAGH,OAAO,CAACN,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}