{"ast": null, "code": "/**\n * Converts `set` to an array of its values.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the values.\n */\nfunction setToArray(set) {\n  var index = -1,\n    result = Array(set.size);\n  set.forEach(function (value) {\n    result[++index] = value;\n  });\n  return result;\n}\nmodule.exports = setToArray;", "map": {"version": 3, "names": ["setToArray", "set", "index", "result", "Array", "size", "for<PERSON>ach", "value", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/lodash/_setToArray.js"], "sourcesContent": ["/**\n * Converts `set` to an array of its values.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the values.\n */\nfunction setToArray(set) {\n  var index = -1,\n      result = Array(set.size);\n\n  set.forEach(function(value) {\n    result[++index] = value;\n  });\n  return result;\n}\n\nmodule.exports = setToArray;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAUA,CAACC,GAAG,EAAE;EACvB,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGC,KAAK,CAACH,GAAG,CAACI,IAAI,CAAC;EAE5BJ,GAAG,CAACK,OAAO,CAAC,UAASC,KAAK,EAAE;IAC1BJ,MAAM,CAAC,EAAED,KAAK,CAAC,GAAGK,KAAK;EACzB,CAAC,CAAC;EACF,OAAOJ,MAAM;AACf;AAEAK,MAAM,CAACC,OAAO,GAAGT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}