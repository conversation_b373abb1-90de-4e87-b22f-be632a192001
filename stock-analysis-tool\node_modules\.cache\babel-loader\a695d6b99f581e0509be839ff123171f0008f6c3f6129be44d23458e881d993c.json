{"ast": null, "code": "import { color } from \"d3-color\";\nimport rgb from \"./rgb.js\";\nimport { genericArray } from \"./array.js\";\nimport date from \"./date.js\";\nimport number from \"./number.js\";\nimport object from \"./object.js\";\nimport string from \"./string.js\";\nimport constant from \"./constant.js\";\nimport numberArray, { isNumberArray } from \"./numberArray.js\";\nexport default function (a, b) {\n  var t = typeof b,\n    c;\n  return b == null || t === \"boolean\" ? constant(b) : (t === \"number\" ? number : t === \"string\" ? (c = color(b)) ? (b = c, rgb) : string : b instanceof color ? rgb : b instanceof Date ? date : isNumberArray(b) ? numberArray : Array.isArray(b) ? genericArray : typeof b.valueOf !== \"function\" && typeof b.toString !== \"function\" || isNaN(b) ? object : number)(a, b);\n}", "map": {"version": 3, "names": ["color", "rgb", "genericArray", "date", "number", "object", "string", "constant", "numberArray", "isNumberArray", "a", "b", "t", "c", "Date", "Array", "isArray", "valueOf", "toString", "isNaN"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/d3-interpolate/src/value.js"], "sourcesContent": ["import {color} from \"d3-color\";\nimport rgb from \"./rgb.js\";\nimport {genericArray} from \"./array.js\";\nimport date from \"./date.js\";\nimport number from \"./number.js\";\nimport object from \"./object.js\";\nimport string from \"./string.js\";\nimport constant from \"./constant.js\";\nimport numberArray, {isNumberArray} from \"./numberArray.js\";\n\nexport default function(a, b) {\n  var t = typeof b, c;\n  return b == null || t === \"boolean\" ? constant(b)\n      : (t === \"number\" ? number\n      : t === \"string\" ? ((c = color(b)) ? (b = c, rgb) : string)\n      : b instanceof color ? rgb\n      : b instanceof Date ? date\n      : isNumberArray(b) ? numberArray\n      : Array.isArray(b) ? genericArray\n      : typeof b.valueOf !== \"function\" && typeof b.toString !== \"function\" || isNaN(b) ? object\n      : number)(a, b);\n}\n"], "mappings": "AAAA,SAAQA,KAAK,QAAO,UAAU;AAC9B,OAAOC,GAAG,MAAM,UAAU;AAC1B,SAAQC,YAAY,QAAO,YAAY;AACvC,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,MAAM,MAAM,aAAa;AAChC,OAAOC,MAAM,MAAM,aAAa;AAChC,OAAOC,MAAM,MAAM,aAAa;AAChC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,WAAW,IAAGC,aAAa,QAAO,kBAAkB;AAE3D,eAAe,UAASC,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAIC,CAAC,GAAG,OAAOD,CAAC;IAAEE,CAAC;EACnB,OAAOF,CAAC,IAAI,IAAI,IAAIC,CAAC,KAAK,SAAS,GAAGL,QAAQ,CAACI,CAAC,CAAC,GAC3C,CAACC,CAAC,KAAK,QAAQ,GAAGR,MAAM,GACxBQ,CAAC,KAAK,QAAQ,GAAI,CAACC,CAAC,GAAGb,KAAK,CAACW,CAAC,CAAC,KAAKA,CAAC,GAAGE,CAAC,EAAEZ,GAAG,IAAIK,MAAM,GACxDK,CAAC,YAAYX,KAAK,GAAGC,GAAG,GACxBU,CAAC,YAAYG,IAAI,GAAGX,IAAI,GACxBM,aAAa,CAACE,CAAC,CAAC,GAAGH,WAAW,GAC9BO,KAAK,CAACC,OAAO,CAACL,CAAC,CAAC,GAAGT,YAAY,GAC/B,OAAOS,CAAC,CAACM,OAAO,KAAK,UAAU,IAAI,OAAON,CAAC,CAACO,QAAQ,KAAK,UAAU,IAAIC,KAAK,CAACR,CAAC,CAAC,GAAGN,MAAM,GACxFD,MAAM,EAAEM,CAAC,EAAEC,CAAC,CAAC;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}