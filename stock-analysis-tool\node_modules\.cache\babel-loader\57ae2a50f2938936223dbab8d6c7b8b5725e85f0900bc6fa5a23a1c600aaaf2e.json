{"ast": null, "code": "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */\nfunction isPrototype(value) {\n  var Ctor = value && value.constructor,\n    proto = typeof Ctor == 'function' && Ctor.prototype || objectProto;\n  return value === proto;\n}\nmodule.exports = isPrototype;", "map": {"version": 3, "names": ["objectProto", "Object", "prototype", "isPrototype", "value", "Ctor", "constructor", "proto", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/lodash/_isPrototype.js"], "sourcesContent": ["/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */\nfunction isPrototype(value) {\n  var Ctor = value && value.constructor,\n      proto = (typeof Ctor == 'function' && Ctor.prototype) || objectProto;\n\n  return value === proto;\n}\n\nmodule.exports = isPrototype;\n"], "mappings": "AAAA;AACA,IAAIA,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAIC,IAAI,GAAGD,KAAK,IAAIA,KAAK,CAACE,WAAW;IACjCC,KAAK,GAAI,OAAOF,IAAI,IAAI,UAAU,IAAIA,IAAI,CAACH,SAAS,IAAKF,WAAW;EAExE,OAAOI,KAAK,KAAKG,KAAK;AACxB;AAEAC,MAAM,CAACC,OAAO,GAAGN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}