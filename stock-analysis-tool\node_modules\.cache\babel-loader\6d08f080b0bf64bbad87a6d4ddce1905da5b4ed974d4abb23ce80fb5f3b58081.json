{"ast": null, "code": "var defineProperty = require('./_defineProperty');\n\n/**\n * The base implementation of `assignValue` and `assignMergeValue` without\n * value checks.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction baseAssignValue(object, key, value) {\n  if (key == '__proto__' && defineProperty) {\n    defineProperty(object, key, {\n      'configurable': true,\n      'enumerable': true,\n      'value': value,\n      'writable': true\n    });\n  } else {\n    object[key] = value;\n  }\n}\nmodule.exports = baseAssignValue;", "map": {"version": 3, "names": ["defineProperty", "require", "baseAssignValue", "object", "key", "value", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/lodash/_baseAssignValue.js"], "sourcesContent": ["var defineProperty = require('./_defineProperty');\n\n/**\n * The base implementation of `assignValue` and `assignMergeValue` without\n * value checks.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction baseAssignValue(object, key, value) {\n  if (key == '__proto__' && defineProperty) {\n    defineProperty(object, key, {\n      'configurable': true,\n      'enumerable': true,\n      'value': value,\n      'writable': true\n    });\n  } else {\n    object[key] = value;\n  }\n}\n\nmodule.exports = baseAssignValue;\n"], "mappings": "AAAA,IAAIA,cAAc,GAAGC,OAAO,CAAC,mBAAmB,CAAC;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAC3C,IAAID,GAAG,IAAI,WAAW,IAAIJ,cAAc,EAAE;IACxCA,cAAc,CAACG,MAAM,EAAEC,GAAG,EAAE;MAC1B,cAAc,EAAE,IAAI;MACpB,YAAY,EAAE,IAAI;MAClB,OAAO,EAAEC,KAAK;MACd,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC,MAAM;IACLF,MAAM,CAACC,GAAG,CAAC,GAAGC,KAAK;EACrB;AACF;AAEAC,MAAM,CAACC,OAAO,GAAGL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}