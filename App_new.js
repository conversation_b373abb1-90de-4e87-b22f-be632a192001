import React, { useState, useEffect } from 'react';
import { Search, ArrowUp, ArrowDown, TrendingUp, Brain, BarChart3, Wifi, WifiOff } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';

// 简化的股票分析工具
const StockAnalysisTool = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStock, setSelectedStock] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [predictions, setPredictions] = useState(null);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // 模拟股票数据
  const mockStocks = {
    'AAPL': {
      symbol: 'AAPL',
      name: 'Apple Inc.',
      price: 185.75,
      change: 2.35,
      changePercent: 1.28,
      volume: 45680000,
      marketCap: 2890000000000,
      pe: 28.5,
      high52: 198.23,
      low52: 124.17,
      sector: 'Technology',
      industry: 'Consumer Electronics'
    },
    'TSLA': {
      symbol: 'TSLA',
      name: 'Tesla, Inc.',
      price: 248.50,
      change: -8.45,
      changePercent: -3.29,
      volume: 98750000,
      marketCap: 789000000000,
      pe: 65.2,
      high52: 299.29,
      low52: 138.80,
      sector: 'Consumer Cyclical',
      industry: 'Auto Manufacturers'
    },
    'MSFT': {
      symbol: 'MSFT',
      name: 'Microsoft Corporation',
      price: 378.85,
      change: 5.20,
      changePercent: 1.39,
      volume: 28450000,
      marketCap: 2810000000000,
      pe: 32.1,
      high52: 384.30,
      low52: 309.45,
      sector: 'Technology',
      industry: 'Software'
    }
  };

  // 生成历史数据
  const generateHistoricalData = () => {
    const data = [];
    let price = selectedStock?.price || 150;
    for (let i = 30; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      price += (Math.random() - 0.5) * 10;
      data.push({
        date: date.toISOString().split('T')[0],
        price: parseFloat(price.toFixed(2)),
        volume: Math.floor(Math.random() * 100000000) + 20000000
      });
    }
    return data;
  };

  // 搜索股票
  const searchStock = (term) => {
    const upperTerm = term.toUpperCase();
    const found = mockStocks[upperTerm];
    
    if (found) {
      setSelectedStock(found);
    } else {
      alert(`未找到股票: ${term}\n\n可搜索的股票: AAPL, TSLA, MSFT`);
    }
  };

  // LSTM预测
  const runPrediction = async () => {
    if (!selectedStock) {
      alert('请先选择一只股票');
      return;
    }
    
    setIsLoading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const currentPrice = selectedStock.price;
    const predictions = [];
    let predictedPrice = currentPrice;
    
    for (let i = 1; i <= 7; i++) {
      predictedPrice += (Math.random() - 0.45) * 5;
      predictions.push({
        day: i,
        predictedPrice: parseFloat(predictedPrice.toFixed(2)),
        confidence: Math.max(0.6, 0.95 - i * 0.05)
      });
    }
    
    setPredictions(predictions);
    setIsLoading(false);
  };

  const handleSearch = () => {
    if (searchTerm.trim()) {
      searchStock(searchTerm.trim());
    }
  };

  const StockCard = ({ stock }) => (
    <div style={{
      backgroundColor: 'white',
      borderRadius: '12px',
      padding: '24px',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
      border: '1px solid #e5e7eb',
      marginBottom: '24px'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '16px' }}>
        <div>
          <h2 style={{ fontSize: '24px', fontWeight: 'bold', color: '#111827', margin: 0 }}>{stock.name}</h2>
          <p style={{ color: '#6b7280', margin: '4px 0 0 0' }}>{stock.symbol}</p>
        </div>
        <div style={{ textAlign: 'right' }}>
          <div style={{ fontSize: '32px', fontWeight: 'bold', color: '#111827' }}>${stock.price}</div>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            color: stock.changePercent >= 0 ? '#059669' : '#dc2626' 
          }}>
            {stock.changePercent >= 0 ? <ArrowUp size={16} style={{ marginRight: '4px' }} /> : <ArrowDown size={16} style={{ marginRight: '4px' }} />}
            ${Math.abs(stock.change)} ({Math.abs(stock.changePercent)}%)
          </div>
        </div>
      </div>
      
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', 
        gap: '16px', 
        fontSize: '14px' 
      }}>
        <div>
          <div style={{ color: '#6b7280' }}>市值</div>
          <div style={{ fontWeight: '600' }}>${(stock.marketCap / 1e9).toFixed(1)}B</div>
        </div>
        <div>
          <div style={{ color: '#6b7280' }}>P/E比率</div>
          <div style={{ fontWeight: '600' }}>{stock.pe}</div>
        </div>
        <div>
          <div style={{ color: '#6b7280' }}>成交量</div>
          <div style={{ fontWeight: '600' }}>{(stock.volume / 1e6).toFixed(1)}M</div>
        </div>
        <div>
          <div style={{ color: '#6b7280' }}>行业</div>
          <div style={{ fontWeight: '600' }}>{stock.industry}</div>
        </div>
      </div>
    </div>
  );

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(to bottom right, #dbeafe, #e0e7ff)',
      padding: '16px'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* Header */}
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', marginBottom: '16px' }}>
            <h1 style={{ fontSize: '36px', fontWeight: 'bold', color: '#111827', marginRight: '16px' }}>
              股票分析工具
            </h1>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {isOnline ? <Wifi size={20} color="#059669" /> : <WifiOff size={20} color="#dc2626" />}
              <span style={{ fontSize: '14px', marginLeft: '4px', color: isOnline ? '#059669' : '#dc2626' }}>
                {isOnline ? '在线' : '离线'}
              </span>
            </div>
          </div>
          <p style={{ color: '#6b7280' }}>基于LSTM深度学习的智能股票分析与预测系统</p>
        </div>

        {/* Search */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '24px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          marginBottom: '32px'
        }}>
          <div style={{ display: 'flex', gap: '16px' }}>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              placeholder="输入股票代码 (如: AAPL, TSLA, MSFT)"
              style={{
                flex: 1,
                padding: '12px 16px',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                fontSize: '16px',
                outline: 'none'
              }}
            />
            <button
              onClick={handleSearch}
              style={{
                backgroundColor: '#2563eb',
                color: 'white',
                padding: '12px 24px',
                borderRadius: '8px',
                border: 'none',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                fontSize: '16px'
              }}
            >
              <Search size={20} style={{ marginRight: '8px' }} />
              搜索
            </button>
          </div>
          
          <div style={{ marginTop: '16px', display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
            <span style={{ fontSize: '14px', color: '#6b7280' }}>快捷选择:</span>
            {Object.keys(mockStocks).map(symbol => (
              <button
                key={symbol}
                onClick={() => {
                  setSearchTerm(symbol);
                  searchStock(symbol);
                }}
                style={{
                  backgroundColor: '#f3f4f6',
                  border: 'none',
                  padding: '4px 12px',
                  borderRadius: '16px',
                  fontSize: '14px',
                  cursor: 'pointer'
                }}
              >
                {symbol}
              </button>
            ))}
          </div>
        </div>

        {/* Stock Info */}
        {selectedStock && (
          <>
            <StockCard stock={selectedStock} />
            
            {/* Charts and Analysis */}
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', 
              gap: '24px', 
              marginBottom: '32px' 
            }}>
              {/* Price Chart */}
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                padding: '24px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
              }}>
                <h3 style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '16px', display: 'flex', alignItems: 'center' }}>
                  <TrendingUp size={20} color="#2563eb" style={{ marginRight: '8px' }} />
                  价格走势
                </h3>
                <ResponsiveContainer width="100%" height={250}>
                  <LineChart data={generateHistoricalData()}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" tick={{ fontSize: 12 }} />
                    <YAxis tick={{ fontSize: 12 }} />
                    <Tooltip />
                    <Line type="monotone" dataKey="price" stroke="#3B82F6" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </div>

              {/* Volume Chart */}
              <div style={{
                backgroundColor: 'white',
                borderRadius: '12px',
                padding: '24px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
              }}>
                <h3 style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '16px', display: 'flex', alignItems: 'center' }}>
                  <BarChart3 size={20} color="#10b981" style={{ marginRight: '8px' }} />
                  成交量
                </h3>
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart data={generateHistoricalData().slice(-7)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" tick={{ fontSize: 12 }} />
                    <YAxis tick={{ fontSize: 12 }} />
                    <Tooltip />
                    <Bar dataKey="volume" fill="#10B981" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* AI Prediction */}
            <div style={{
              backgroundColor: 'white',
              borderRadius: '12px',
              padding: '24px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                <h3 style={{ fontSize: '20px', fontWeight: 'bold', display: 'flex', alignItems: 'center', margin: 0 }}>
                  <Brain size={20} color="#8b5cf6" style={{ marginRight: '8px' }} />
                  LSTM预测分析
                </h3>
                <button
                  onClick={runPrediction}
                  disabled={isLoading}
                  style={{
                    backgroundColor: '#8b5cf6',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    border: 'none',
                    cursor: isLoading ? 'not-allowed' : 'pointer',
                    opacity: isLoading ? 0.5 : 1
                  }}
                >
                  {isLoading ? '分析中...' : '运行预测'}
                </button>
              </div>
              
              {isLoading && (
                <div style={{ textAlign: 'center', padding: '32px' }}>
                  <div style={{ 
                    width: '32px', 
                    height: '32px', 
                    border: '3px solid #f3f4f6', 
                    borderTop: '3px solid #8b5cf6', 
                    borderRadius: '50%', 
                    animation: 'spin 1s linear infinite',
                    margin: '0 auto 16px'
                  }}></div>
                  <p style={{ color: '#6b7280' }}>正在运行LSTM神经网络预测...</p>
                </div>
              )}
              
              {predictions && !isLoading && (
                <div>
                  <ResponsiveContainer width="100%" height={200}>
                    <LineChart data={predictions}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="day" />
                      <YAxis />
                      <Tooltip />
                      <Line type="monotone" dataKey="predictedPrice" stroke="#8B5CF6" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                  <div style={{ 
                    marginTop: '16px', 
                    display: 'grid', 
                    gridTemplateColumns: 'repeat(auto-fit, minmax(80px, 1fr))', 
                    gap: '8px' 
                  }}>
                    {predictions.map((pred, idx) => (
                      <div key={idx} style={{ 
                        padding: '8px', 
                        backgroundColor: '#f8fafc', 
                        borderRadius: '8px', 
                        textAlign: 'center' 
                      }}>
                        <div style={{ fontSize: '12px', color: '#6b7280' }}>第{pred.day}天</div>
                        <div style={{ fontWeight: 'bold', fontSize: '14px' }}>${pred.predictedPrice}</div>
                        <div style={{ fontSize: '12px', color: '#059669' }}>{(pred.confidence * 100).toFixed(0)}%</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </>
        )}
        
        {!selectedStock && (
          <div style={{ textAlign: 'center', padding: '48px' }}>
            <div style={{ fontSize: '64px', marginBottom: '16px' }}>📈</div>
            <h3 style={{ fontSize: '20px', fontWeight: '600', color: '#6b7280', marginBottom: '8px' }}>开始分析股票</h3>
            <p style={{ color: '#9ca3af' }}>在上方搜索框中输入股票代码开始分析</p>
          </div>
        )}
      </div>
      
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default StockAnalysisTool;
