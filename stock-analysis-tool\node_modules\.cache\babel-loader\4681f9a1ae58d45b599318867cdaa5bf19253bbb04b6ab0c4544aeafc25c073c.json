{"ast": null, "code": "/** Used to match `RegExp` flags from their coerced string values. */\nvar reFlags = /\\w*$/;\n\n/**\n * Creates a clone of `regexp`.\n *\n * @private\n * @param {Object} regexp The regexp to clone.\n * @returns {Object} Returns the cloned regexp.\n */\nfunction cloneRegExp(regexp) {\n  var result = new regexp.constructor(regexp.source, reFlags.exec(regexp));\n  result.lastIndex = regexp.lastIndex;\n  return result;\n}\nmodule.exports = cloneRegExp;", "map": {"version": 3, "names": ["reFlags", "cloneRegExp", "regexp", "result", "constructor", "source", "exec", "lastIndex", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/lodash/_cloneRegExp.js"], "sourcesContent": ["/** Used to match `RegExp` flags from their coerced string values. */\nvar reFlags = /\\w*$/;\n\n/**\n * Creates a clone of `regexp`.\n *\n * @private\n * @param {Object} regexp The regexp to clone.\n * @returns {Object} Returns the cloned regexp.\n */\nfunction cloneRegExp(regexp) {\n  var result = new regexp.constructor(regexp.source, reFlags.exec(regexp));\n  result.lastIndex = regexp.lastIndex;\n  return result;\n}\n\nmodule.exports = cloneRegExp;\n"], "mappings": "AAAA;AACA,IAAIA,OAAO,GAAG,MAAM;;AAEpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,MAAM,EAAE;EAC3B,IAAIC,MAAM,GAAG,IAAID,MAAM,CAACE,WAAW,CAACF,MAAM,CAACG,MAAM,EAAEL,OAAO,CAACM,IAAI,CAACJ,MAAM,CAAC,CAAC;EACxEC,MAAM,CAACI,SAAS,GAAGL,MAAM,CAACK,SAAS;EACnC,OAAOJ,MAAM;AACf;AAEAK,MAAM,CAACC,OAAO,GAAGR,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}