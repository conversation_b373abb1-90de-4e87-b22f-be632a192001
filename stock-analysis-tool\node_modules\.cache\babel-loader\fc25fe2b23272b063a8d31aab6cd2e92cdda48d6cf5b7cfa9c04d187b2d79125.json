{"ast": null, "code": "var baseExtremum = require('./_baseExtremum'),\n  baseIteratee = require('./_baseIteratee'),\n  baseLt = require('./_baseLt');\n\n/**\n * This method is like `_.min` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the criterion by which\n * the value is ranked. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {*} Returns the minimum value.\n * @example\n *\n * var objects = [{ 'n': 1 }, { 'n': 2 }];\n *\n * _.minBy(objects, function(o) { return o.n; });\n * // => { 'n': 1 }\n *\n * // The `_.property` iteratee shorthand.\n * _.minBy(objects, 'n');\n * // => { 'n': 1 }\n */\nfunction minBy(array, iteratee) {\n  return array && array.length ? baseExtremum(array, baseIteratee(iteratee, 2), baseLt) : undefined;\n}\nmodule.exports = minBy;", "map": {"version": 3, "names": ["baseExtremum", "require", "baseIteratee", "baseLt", "minBy", "array", "iteratee", "length", "undefined", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/lodash/minBy.js"], "sourcesContent": ["var baseExtremum = require('./_baseExtremum'),\n    baseIteratee = require('./_baseIteratee'),\n    baseLt = require('./_baseLt');\n\n/**\n * This method is like `_.min` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the criterion by which\n * the value is ranked. The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {*} Returns the minimum value.\n * @example\n *\n * var objects = [{ 'n': 1 }, { 'n': 2 }];\n *\n * _.minBy(objects, function(o) { return o.n; });\n * // => { 'n': 1 }\n *\n * // The `_.property` iteratee shorthand.\n * _.minBy(objects, 'n');\n * // => { 'n': 1 }\n */\nfunction minBy(array, iteratee) {\n  return (array && array.length)\n    ? baseExtremum(array, baseIteratee(iteratee, 2), baseLt)\n    : undefined;\n}\n\nmodule.exports = minBy;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAiB,CAAC;EACzCC,YAAY,GAAGD,OAAO,CAAC,iBAAiB,CAAC;EACzCE,MAAM,GAAGF,OAAO,CAAC,WAAW,CAAC;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,KAAKA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC9B,OAAQD,KAAK,IAAIA,KAAK,CAACE,MAAM,GACzBP,YAAY,CAACK,KAAK,EAAEH,YAAY,CAACI,QAAQ,EAAE,CAAC,CAAC,EAAEH,MAAM,CAAC,GACtDK,SAAS;AACf;AAEAC,MAAM,CAACC,OAAO,GAAGN,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}