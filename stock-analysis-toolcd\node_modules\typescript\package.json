{"name": "typescript", "author": "Microsoft Corp.", "homepage": "https://www.typescriptlang.org/", "version": "4.9.5", "license": "Apache-2.0", "description": "TypeScript is a language for application scale JavaScript development", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript"], "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "repository": {"type": "git", "url": "https://github.com/Microsoft/TypeScript.git"}, "main": "./lib/typescript.js", "typings": "./lib/typescript.d.ts", "bin": {"tsc": "./bin/tsc", "tsserver": "./bin/tsserver"}, "engines": {"node": ">=4.2.0"}, "files": ["bin", "lib", "!lib/enu", "LICENSE.txt", "README.md", "SECURITY.md", "ThirdPartyNoticeText.txt", "!**/.gitattributes"], "devDependencies": {"@octokit/rest": "latest", "@types/chai": "latest", "@types/fancy-log": "^2.0.0", "@types/fs-extra": "^9.0.13", "@types/glob": "latest", "@types/gulp": "^4.0.9", "@types/gulp-concat": "latest", "@types/gulp-newer": "latest", "@types/gulp-rename": "latest", "@types/gulp-sourcemaps": "latest", "@types/merge2": "latest", "@types/microsoft__typescript-etw": "latest", "@types/minimist": "latest", "@types/mkdirp": "latest", "@types/mocha": "latest", "@types/ms": "latest", "@types/node": "latest", "@types/source-map-support": "latest", "@types/which": "^2.0.1", "@types/xml2js": "^0.4.11", "@typescript-eslint/eslint-plugin": "^5.33.1", "@typescript-eslint/parser": "^5.33.1", "@typescript-eslint/utils": "^5.33.1", "azure-devops-node-api": "^11.2.0", "chai": "latest", "chalk": "^4.1.2", "del": "^6.1.1", "diff": "^5.1.0", "eslint": "^8.22.0", "eslint-formatter-autolinkable-stylish": "^1.2.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsdoc": "^39.3.6", "eslint-plugin-local": "^1.0.0", "eslint-plugin-no-null": "^1.0.2", "fancy-log": "latest", "fs-extra": "^9.1.0", "glob": "latest", "gulp": "^4.0.2", "gulp-concat": "latest", "gulp-insert": "latest", "gulp-newer": "latest", "gulp-rename": "latest", "gulp-sourcemaps": "latest", "merge2": "latest", "minimist": "latest", "mkdirp": "latest", "mocha": "latest", "mocha-fivemat-progress-reporter": "latest", "ms": "^2.1.3", "node-fetch": "^3.2.10", "source-map-support": "latest", "typescript": "^4.8.4", "vinyl": "latest", "which": "^2.0.2", "xml2js": "^0.4.23"}, "overrides": {"es5-ext": "0.10.53"}, "scripts": {"test": "gulp runtests-parallel --light=false", "test:eslint-rules": "gulp run-eslint-rules-tests", "build": "npm run build:compiler && npm run build:tests", "build:compiler": "gulp local", "build:tests": "gulp tests", "start": "node lib/tsc", "clean": "gulp clean", "gulp": "gulp", "lint": "gulp lint", "setup-hooks": "node scripts/link-hooks.mjs"}, "browser": {"fs": false, "os": false, "path": false, "crypto": false, "buffer": false, "@microsoft/typescript-etw": false, "source-map-support": false, "inspector": false}, "packageManager": "npm@8.15.0", "volta": {"node": "14.20.0", "npm": "8.15.0"}}