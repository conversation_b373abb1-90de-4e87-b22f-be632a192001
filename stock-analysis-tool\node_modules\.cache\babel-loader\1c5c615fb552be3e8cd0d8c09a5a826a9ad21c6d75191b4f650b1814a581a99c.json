{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport PropTypes from 'prop-types';\nimport React from 'react';\nimport TransitionGroupContext from './TransitionGroupContext';\nimport { getChildMapping, getInitialChildMapping, getNextChildMapping } from './utils/ChildMapping';\nvar values = Object.values || function (obj) {\n  return Object.keys(obj).map(function (k) {\n    return obj[k];\n  });\n};\nvar defaultProps = {\n  component: 'div',\n  childFactory: function childFactory(child) {\n    return child;\n  }\n};\n/**\n * The `<TransitionGroup>` component manages a set of transition components\n * (`<Transition>` and `<CSSTransition>`) in a list. Like with the transition\n * components, `<TransitionGroup>` is a state machine for managing the mounting\n * and unmounting of components over time.\n *\n * Consider the example below. As items are removed or added to the TodoList the\n * `in` prop is toggled automatically by the `<TransitionGroup>`.\n *\n * Note that `<TransitionGroup>`  does not define any animation behavior!\n * Exactly _how_ a list item animates is up to the individual transition\n * component. This means you can mix and match animations across different list\n * items.\n */\n\nvar TransitionGroup = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(TransitionGroup, _React$Component);\n  function TransitionGroup(props, context) {\n    var _this;\n    _this = _React$Component.call(this, props, context) || this;\n    var handleExited = _this.handleExited.bind(_assertThisInitialized(_this)); // Initial children should all be entering, dependent on appear\n\n    _this.state = {\n      contextValue: {\n        isMounting: true\n      },\n      handleExited: handleExited,\n      firstRender: true\n    };\n    return _this;\n  }\n  var _proto = TransitionGroup.prototype;\n  _proto.componentDidMount = function componentDidMount() {\n    this.mounted = true;\n    this.setState({\n      contextValue: {\n        isMounting: false\n      }\n    });\n  };\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.mounted = false;\n  };\n  TransitionGroup.getDerivedStateFromProps = function getDerivedStateFromProps(nextProps, _ref) {\n    var prevChildMapping = _ref.children,\n      handleExited = _ref.handleExited,\n      firstRender = _ref.firstRender;\n    return {\n      children: firstRender ? getInitialChildMapping(nextProps, handleExited) : getNextChildMapping(nextProps, prevChildMapping, handleExited),\n      firstRender: false\n    };\n  } // node is `undefined` when user provided `nodeRef` prop\n  ;\n  _proto.handleExited = function handleExited(child, node) {\n    var currentChildMapping = getChildMapping(this.props.children);\n    if (child.key in currentChildMapping) return;\n    if (child.props.onExited) {\n      child.props.onExited(node);\n    }\n    if (this.mounted) {\n      this.setState(function (state) {\n        var children = _extends({}, state.children);\n        delete children[child.key];\n        return {\n          children: children\n        };\n      });\n    }\n  };\n  _proto.render = function render() {\n    var _this$props = this.props,\n      Component = _this$props.component,\n      childFactory = _this$props.childFactory,\n      props = _objectWithoutPropertiesLoose(_this$props, [\"component\", \"childFactory\"]);\n    var contextValue = this.state.contextValue;\n    var children = values(this.state.children).map(childFactory);\n    delete props.appear;\n    delete props.enter;\n    delete props.exit;\n    if (Component === null) {\n      return /*#__PURE__*/React.createElement(TransitionGroupContext.Provider, {\n        value: contextValue\n      }, children);\n    }\n    return /*#__PURE__*/React.createElement(TransitionGroupContext.Provider, {\n      value: contextValue\n    }, /*#__PURE__*/React.createElement(Component, props, children));\n  };\n  return TransitionGroup;\n}(React.Component);\nTransitionGroup.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /**\n   * `<TransitionGroup>` renders a `<div>` by default. You can change this\n   * behavior by providing a `component` prop.\n   * If you use React v16+ and would like to avoid a wrapping `<div>` element\n   * you can pass in `component={null}`. This is useful if the wrapping div\n   * borks your css styles.\n   */\n  component: PropTypes.any,\n  /**\n   * A set of `<Transition>` components, that are toggled `in` and out as they\n   * leave. the `<TransitionGroup>` will inject specific transition props, so\n   * remember to spread them through if you are wrapping the `<Transition>` as\n   * with our `<Fade>` example.\n   *\n   * While this component is meant for multiple `Transition` or `CSSTransition`\n   * children, sometimes you may want to have a single transition child with\n   * content that you want to be transitioned out and in when you change it\n   * (e.g. routes, images etc.) In that case you can change the `key` prop of\n   * the transition child as you change its content, this will cause\n   * `TransitionGroup` to transition the child out and back in.\n   */\n  children: PropTypes.node,\n  /**\n   * A convenience prop that enables or disables appear animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  appear: PropTypes.bool,\n  /**\n   * A convenience prop that enables or disables enter animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  enter: PropTypes.bool,\n  /**\n   * A convenience prop that enables or disables exit animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  exit: PropTypes.bool,\n  /**\n   * You may need to apply reactive updates to a child as it is exiting.\n   * This is generally done by using `cloneElement` however in the case of an exiting\n   * child the element has already been removed and not accessible to the consumer.\n   *\n   * If you do need to update a child as it leaves you can provide a `childFactory`\n   * to wrap every child, even the ones that are leaving.\n   *\n   * @type Function(child: ReactElement) -> ReactElement\n   */\n  childFactory: PropTypes.func\n} : {};\nTransitionGroup.defaultProps = defaultProps;\nexport default TransitionGroup;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_assertThisInitialized", "_inherits<PERSON><PERSON>e", "PropTypes", "React", "TransitionGroupContext", "get<PERSON>hildMapping", "getInitialChildMapping", "getNextChildMapping", "values", "Object", "obj", "keys", "map", "k", "defaultProps", "component", "childFactory", "child", "TransitionGroup", "_React$Component", "props", "context", "_this", "call", "handleExited", "bind", "state", "contextValue", "isMounting", "firstRender", "_proto", "prototype", "componentDidMount", "mounted", "setState", "componentWillUnmount", "getDerivedStateFromProps", "nextProps", "_ref", "prevChildMapping", "children", "node", "currentChildMapping", "key", "onExited", "render", "_this$props", "Component", "appear", "enter", "exit", "createElement", "Provider", "value", "propTypes", "process", "env", "NODE_ENV", "any", "bool", "func"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/react-transition-group/esm/TransitionGroup.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport PropTypes from 'prop-types';\nimport React from 'react';\nimport TransitionGroupContext from './TransitionGroupContext';\nimport { getChildMapping, getInitialChildMapping, getNextChildMapping } from './utils/ChildMapping';\n\nvar values = Object.values || function (obj) {\n  return Object.keys(obj).map(function (k) {\n    return obj[k];\n  });\n};\n\nvar defaultProps = {\n  component: 'div',\n  childFactory: function childFactory(child) {\n    return child;\n  }\n};\n/**\n * The `<TransitionGroup>` component manages a set of transition components\n * (`<Transition>` and `<CSSTransition>`) in a list. Like with the transition\n * components, `<TransitionGroup>` is a state machine for managing the mounting\n * and unmounting of components over time.\n *\n * Consider the example below. As items are removed or added to the TodoList the\n * `in` prop is toggled automatically by the `<TransitionGroup>`.\n *\n * Note that `<TransitionGroup>`  does not define any animation behavior!\n * Exactly _how_ a list item animates is up to the individual transition\n * component. This means you can mix and match animations across different list\n * items.\n */\n\nvar TransitionGroup = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(TransitionGroup, _React$Component);\n\n  function TransitionGroup(props, context) {\n    var _this;\n\n    _this = _React$Component.call(this, props, context) || this;\n\n    var handleExited = _this.handleExited.bind(_assertThisInitialized(_this)); // Initial children should all be entering, dependent on appear\n\n\n    _this.state = {\n      contextValue: {\n        isMounting: true\n      },\n      handleExited: handleExited,\n      firstRender: true\n    };\n    return _this;\n  }\n\n  var _proto = TransitionGroup.prototype;\n\n  _proto.componentDidMount = function componentDidMount() {\n    this.mounted = true;\n    this.setState({\n      contextValue: {\n        isMounting: false\n      }\n    });\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.mounted = false;\n  };\n\n  TransitionGroup.getDerivedStateFromProps = function getDerivedStateFromProps(nextProps, _ref) {\n    var prevChildMapping = _ref.children,\n        handleExited = _ref.handleExited,\n        firstRender = _ref.firstRender;\n    return {\n      children: firstRender ? getInitialChildMapping(nextProps, handleExited) : getNextChildMapping(nextProps, prevChildMapping, handleExited),\n      firstRender: false\n    };\n  } // node is `undefined` when user provided `nodeRef` prop\n  ;\n\n  _proto.handleExited = function handleExited(child, node) {\n    var currentChildMapping = getChildMapping(this.props.children);\n    if (child.key in currentChildMapping) return;\n\n    if (child.props.onExited) {\n      child.props.onExited(node);\n    }\n\n    if (this.mounted) {\n      this.setState(function (state) {\n        var children = _extends({}, state.children);\n\n        delete children[child.key];\n        return {\n          children: children\n        };\n      });\n    }\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        Component = _this$props.component,\n        childFactory = _this$props.childFactory,\n        props = _objectWithoutPropertiesLoose(_this$props, [\"component\", \"childFactory\"]);\n\n    var contextValue = this.state.contextValue;\n    var children = values(this.state.children).map(childFactory);\n    delete props.appear;\n    delete props.enter;\n    delete props.exit;\n\n    if (Component === null) {\n      return /*#__PURE__*/React.createElement(TransitionGroupContext.Provider, {\n        value: contextValue\n      }, children);\n    }\n\n    return /*#__PURE__*/React.createElement(TransitionGroupContext.Provider, {\n      value: contextValue\n    }, /*#__PURE__*/React.createElement(Component, props, children));\n  };\n\n  return TransitionGroup;\n}(React.Component);\n\nTransitionGroup.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  /**\n   * `<TransitionGroup>` renders a `<div>` by default. You can change this\n   * behavior by providing a `component` prop.\n   * If you use React v16+ and would like to avoid a wrapping `<div>` element\n   * you can pass in `component={null}`. This is useful if the wrapping div\n   * borks your css styles.\n   */\n  component: PropTypes.any,\n\n  /**\n   * A set of `<Transition>` components, that are toggled `in` and out as they\n   * leave. the `<TransitionGroup>` will inject specific transition props, so\n   * remember to spread them through if you are wrapping the `<Transition>` as\n   * with our `<Fade>` example.\n   *\n   * While this component is meant for multiple `Transition` or `CSSTransition`\n   * children, sometimes you may want to have a single transition child with\n   * content that you want to be transitioned out and in when you change it\n   * (e.g. routes, images etc.) In that case you can change the `key` prop of\n   * the transition child as you change its content, this will cause\n   * `TransitionGroup` to transition the child out and back in.\n   */\n  children: PropTypes.node,\n\n  /**\n   * A convenience prop that enables or disables appear animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  appear: PropTypes.bool,\n\n  /**\n   * A convenience prop that enables or disables enter animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  enter: PropTypes.bool,\n\n  /**\n   * A convenience prop that enables or disables exit animations\n   * for all children. Note that specifying this will override any defaults set\n   * on individual children Transitions.\n   */\n  exit: PropTypes.bool,\n\n  /**\n   * You may need to apply reactive updates to a child as it is exiting.\n   * This is generally done by using `cloneElement` however in the case of an exiting\n   * child the element has already been removed and not accessible to the consumer.\n   *\n   * If you do need to update a child as it leaves you can provide a `childFactory`\n   * to wrap every child, even the ones that are leaving.\n   *\n   * @type Function(child: ReactElement) -> ReactElement\n   */\n  childFactory: PropTypes.func\n} : {};\nTransitionGroup.defaultProps = defaultProps;\nexport default TransitionGroup;"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,SAASC,eAAe,EAAEC,sBAAsB,EAAEC,mBAAmB,QAAQ,sBAAsB;AAEnG,IAAIC,MAAM,GAAGC,MAAM,CAACD,MAAM,IAAI,UAAUE,GAAG,EAAE;EAC3C,OAAOD,MAAM,CAACE,IAAI,CAACD,GAAG,CAAC,CAACE,GAAG,CAAC,UAAUC,CAAC,EAAE;IACvC,OAAOH,GAAG,CAACG,CAAC,CAAC;EACf,CAAC,CAAC;AACJ,CAAC;AAED,IAAIC,YAAY,GAAG;EACjBC,SAAS,EAAE,KAAK;EAChBC,YAAY,EAAE,SAASA,YAAYA,CAACC,KAAK,EAAE;IACzC,OAAOA,KAAK;EACd;AACF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,eAAe,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAC7DlB,cAAc,CAACiB,eAAe,EAAEC,gBAAgB,CAAC;EAEjD,SAASD,eAAeA,CAACE,KAAK,EAAEC,OAAO,EAAE;IACvC,IAAIC,KAAK;IAETA,KAAK,GAAGH,gBAAgB,CAACI,IAAI,CAAC,IAAI,EAAEH,KAAK,EAAEC,OAAO,CAAC,IAAI,IAAI;IAE3D,IAAIG,YAAY,GAAGF,KAAK,CAACE,YAAY,CAACC,IAAI,CAACzB,sBAAsB,CAACsB,KAAK,CAAC,CAAC,CAAC,CAAC;;IAG3EA,KAAK,CAACI,KAAK,GAAG;MACZC,YAAY,EAAE;QACZC,UAAU,EAAE;MACd,CAAC;MACDJ,YAAY,EAAEA,YAAY;MAC1BK,WAAW,EAAE;IACf,CAAC;IACD,OAAOP,KAAK;EACd;EAEA,IAAIQ,MAAM,GAAGZ,eAAe,CAACa,SAAS;EAEtCD,MAAM,CAACE,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACtD,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,QAAQ,CAAC;MACZP,YAAY,EAAE;QACZC,UAAU,EAAE;MACd;IACF,CAAC,CAAC;EACJ,CAAC;EAEDE,MAAM,CAACK,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;IAC5D,IAAI,CAACF,OAAO,GAAG,KAAK;EACtB,CAAC;EAEDf,eAAe,CAACkB,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,SAAS,EAAEC,IAAI,EAAE;IAC5F,IAAIC,gBAAgB,GAAGD,IAAI,CAACE,QAAQ;MAChChB,YAAY,GAAGc,IAAI,CAACd,YAAY;MAChCK,WAAW,GAAGS,IAAI,CAACT,WAAW;IAClC,OAAO;MACLW,QAAQ,EAAEX,WAAW,GAAGvB,sBAAsB,CAAC+B,SAAS,EAAEb,YAAY,CAAC,GAAGjB,mBAAmB,CAAC8B,SAAS,EAAEE,gBAAgB,EAAEf,YAAY,CAAC;MACxIK,WAAW,EAAE;IACf,CAAC;EACH,CAAC,CAAC;EAAA;EAGFC,MAAM,CAACN,YAAY,GAAG,SAASA,YAAYA,CAACP,KAAK,EAAEwB,IAAI,EAAE;IACvD,IAAIC,mBAAmB,GAAGrC,eAAe,CAAC,IAAI,CAACe,KAAK,CAACoB,QAAQ,CAAC;IAC9D,IAAIvB,KAAK,CAAC0B,GAAG,IAAID,mBAAmB,EAAE;IAEtC,IAAIzB,KAAK,CAACG,KAAK,CAACwB,QAAQ,EAAE;MACxB3B,KAAK,CAACG,KAAK,CAACwB,QAAQ,CAACH,IAAI,CAAC;IAC5B;IAEA,IAAI,IAAI,CAACR,OAAO,EAAE;MAChB,IAAI,CAACC,QAAQ,CAAC,UAAUR,KAAK,EAAE;QAC7B,IAAIc,QAAQ,GAAGzC,QAAQ,CAAC,CAAC,CAAC,EAAE2B,KAAK,CAACc,QAAQ,CAAC;QAE3C,OAAOA,QAAQ,CAACvB,KAAK,CAAC0B,GAAG,CAAC;QAC1B,OAAO;UACLH,QAAQ,EAAEA;QACZ,CAAC;MACH,CAAC,CAAC;IACJ;EACF,CAAC;EAEDV,MAAM,CAACe,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAChC,IAAIC,WAAW,GAAG,IAAI,CAAC1B,KAAK;MACxB2B,SAAS,GAAGD,WAAW,CAAC/B,SAAS;MACjCC,YAAY,GAAG8B,WAAW,CAAC9B,YAAY;MACvCI,KAAK,GAAGtB,6BAA6B,CAACgD,WAAW,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;IAErF,IAAInB,YAAY,GAAG,IAAI,CAACD,KAAK,CAACC,YAAY;IAC1C,IAAIa,QAAQ,GAAGhC,MAAM,CAAC,IAAI,CAACkB,KAAK,CAACc,QAAQ,CAAC,CAAC5B,GAAG,CAACI,YAAY,CAAC;IAC5D,OAAOI,KAAK,CAAC4B,MAAM;IACnB,OAAO5B,KAAK,CAAC6B,KAAK;IAClB,OAAO7B,KAAK,CAAC8B,IAAI;IAEjB,IAAIH,SAAS,KAAK,IAAI,EAAE;MACtB,OAAO,aAAa5C,KAAK,CAACgD,aAAa,CAAC/C,sBAAsB,CAACgD,QAAQ,EAAE;QACvEC,KAAK,EAAE1B;MACT,CAAC,EAAEa,QAAQ,CAAC;IACd;IAEA,OAAO,aAAarC,KAAK,CAACgD,aAAa,CAAC/C,sBAAsB,CAACgD,QAAQ,EAAE;MACvEC,KAAK,EAAE1B;IACT,CAAC,EAAE,aAAaxB,KAAK,CAACgD,aAAa,CAACJ,SAAS,EAAE3B,KAAK,EAAEoB,QAAQ,CAAC,CAAC;EAClE,CAAC;EAED,OAAOtB,eAAe;AACxB,CAAC,CAACf,KAAK,CAAC4C,SAAS,CAAC;AAElB7B,eAAe,CAACoC,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG;EAClE;AACF;AACA;AACA;AACA;AACA;AACA;EACE1C,SAAS,EAAEb,SAAS,CAACwD,GAAG;EAExB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACElB,QAAQ,EAAEtC,SAAS,CAACuC,IAAI;EAExB;AACF;AACA;AACA;AACA;EACEO,MAAM,EAAE9C,SAAS,CAACyD,IAAI;EAEtB;AACF;AACA;AACA;AACA;EACEV,KAAK,EAAE/C,SAAS,CAACyD,IAAI;EAErB;AACF;AACA;AACA;AACA;EACET,IAAI,EAAEhD,SAAS,CAACyD,IAAI;EAEpB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE3C,YAAY,EAAEd,SAAS,CAAC0D;AAC1B,CAAC,GAAG,CAAC,CAAC;AACN1C,eAAe,CAACJ,YAAY,GAAGA,YAAY;AAC3C,eAAeI,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}