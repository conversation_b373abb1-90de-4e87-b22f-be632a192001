<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票搜索功能测试</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }
    </style>
</head>
<body>
    <div id="root"></div>
    
    <script type="text/babel">
        // 简化的搜索功能测试
        const { useState } = React;
        
        const mockStockData = {
            'AAPL': {
                symbol: 'AAPL',
                name: '苹果公司',
                aliases: ['苹果', 'Apple', 'iPhone', '库克', 'iOS'],
                price: 185.75,
                changePercent: 1.28,
                sector: '科技',
                industry: '消费电子'
            },
            'TSLA': {
                symbol: 'TSLA',
                name: '特斯拉',
                aliases: ['特斯拉', 'Tesla', '马斯克', '电动车', '新能源汽车', 'Model'],
                price: 248.50,
                changePercent: -3.29,
                sector: '汽车',
                industry: '电动汽车'
            },
            'MSFT': {
                symbol: 'MSFT',
                name: '微软',
                aliases: ['微软', 'Microsoft', 'Windows', 'Office', 'Azure', '比尔盖茨'],
                price: 378.85,
                changePercent: 1.39,
                sector: '科技',
                industry: '软件'
            }
        };
        
        const SearchTest = () => {
            const [searchTerm, setSearchTerm] = useState('');
            const [result, setResult] = useState(null);
            const [suggestions, setSuggestions] = useState([]);
            
            const searchStock = (term) => {
                if (!term || !term.trim()) return null;
                
                const searchTerm = term.trim();
                const upperTerm = searchTerm.toUpperCase();
                const lowerTerm = searchTerm.toLowerCase();
                
                let found = null;
                let bestMatch = null;
                let maxScore = 0;
                
                // 精确匹配股票代码
                if (mockStockData[upperTerm]) {
                    found = mockStockData[upperTerm];
                } else {
                    // 智能模糊匹配
                    Object.values(mockStockData).forEach(stock => {
                        let score = 0;
                        
                        // 股票代码匹配
                        if (stock.symbol.toLowerCase().includes(lowerTerm)) {
                            score += 100;
                        }
                        
                        // 公司名称匹配
                        if (stock.name === searchTerm) {
                            score += 90;
                        } else if (stock.name.includes(searchTerm)) {
                            score += 80;
                        } else if (stock.name.toLowerCase().includes(lowerTerm)) {
                            score += 60;
                        }
                        
                        // 别名匹配
                        stock.aliases.forEach(alias => {
                            if (alias === searchTerm) {
                                score += 85;
                            } else if (alias.toLowerCase() === lowerTerm) {
                                score += 75;
                            } else if (alias.toLowerCase().includes(lowerTerm)) {
                                score += 50;
                            }
                        });
                        
                        if (score > maxScore) {
                            maxScore = score;
                            bestMatch = stock;
                        }
                    });
                    
                    if (maxScore >= 30) {
                        found = bestMatch;
                    }
                }
                
                return found;
            };
            
            const generateSuggestions = (term) => {
                if (!term || term.length < 1) return [];
                
                const lowerTerm = term.toLowerCase();
                const suggestions = [];
                
                Object.values(mockStockData).forEach(stock => {
                    let relevance = 0;
                    
                    if (stock.name.toLowerCase().includes(lowerTerm)) {
                        relevance += 2;
                    }
                    
                    stock.aliases.forEach(alias => {
                        if (alias.toLowerCase().includes(lowerTerm)) {
                            relevance += 1;
                        }
                    });
                    
                    if (relevance > 0) {
                        suggestions.push({ ...stock, relevance });
                    }
                });
                
                return suggestions.sort((a, b) => b.relevance - a.relevance);
            };
            
            const handleSearch = () => {
                const found = searchStock(searchTerm);
                setResult(found);
            };
            
            const handleInputChange = (e) => {
                const value = e.target.value;
                setSearchTerm(value);
                setSuggestions(generateSuggestions(value));
            };
            
            const testCases = [
                'AAPL', '苹果', 'Apple', 'iPhone',
                'TSLA', '特斯拉', 'Tesla', '马斯克',
                'MSFT', '微软', 'Microsoft', 'Windows'
            ];
            
            return (
                <div className="max-w-4xl mx-auto p-6">
                    <h1 className="text-3xl font-bold mb-6 text-center">股票搜索功能测试</h1>
                    
                    <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
                        <div className="flex gap-4 mb-4">
                            <input
                                type="text"
                                value={searchTerm}
                                onChange={handleInputChange}
                                placeholder="输入股票代码或公司名称"
                                className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            />
                            <button
                                onClick={handleSearch}
                                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
                            >
                                搜索
                            </button>
                        </div>
                        
                        {suggestions.length > 0 && (
                            <div className="mb-4">
                                <h3 className="font-semibold mb-2">搜索建议:</h3>
                                <div className="space-y-2">
                                    {suggestions.map(stock => (
                                        <div key={stock.symbol} className="p-2 bg-gray-50 rounded">
                                            <strong>{stock.symbol}</strong> - {stock.name}
                                            <div className="text-sm text-gray-600">
                                                别名: {stock.aliases.join(', ')}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                        
                        {result && (
                            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                <h3 className="font-semibold text-green-800 mb-2">搜索结果:</h3>
                                <div>
                                    <strong>{result.symbol}</strong> - {result.name}
                                    <div className="text-sm text-gray-600">
                                        价格: ${result.price} ({result.changePercent}%)
                                    </div>
                                    <div className="text-sm text-gray-600">
                                        行业: {result.sector} • {result.industry}
                                    </div>
                                </div>
                            </div>
                        )}
                        
                        {searchTerm && !result && (
                            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                                <p className="text-red-800">未找到匹配的股票</p>
                            </div>
                        )}
                    </div>
                    
                    <div className="bg-white rounded-lg shadow-lg p-6">
                        <h3 className="font-semibold mb-4">快速测试:</h3>
                        <div className="grid grid-cols-3 md:grid-cols-4 gap-2">
                            {testCases.map(testCase => (
                                <button
                                    key={testCase}
                                    onClick={() => {
                                        setSearchTerm(testCase);
                                        setResult(searchStock(testCase));
                                        setSuggestions(generateSuggestions(testCase));
                                    }}
                                    className="bg-gray-100 hover:bg-gray-200 px-3 py-2 rounded text-sm"
                                >
                                    {testCase}
                                </button>
                            ))}
                        </div>
                    </div>
                </div>
            );
        };
        
        ReactDOM.render(<SearchTest />, document.getElementById('root'));
    </script>
</body>
</html>
