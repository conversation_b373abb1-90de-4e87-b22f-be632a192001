{"ast": null, "code": "import ascending from \"./ascending.js\";\nimport descending from \"./descending.js\";\nexport default function bisector(f) {\n  let compare1, compare2, delta;\n\n  // If an accessor is specified, promote it to a comparator. In this case we\n  // can test whether the search value is (self-) comparable. We can’t do this\n  // for a comparator (except for specific, known comparators) because we can’t\n  // tell if the comparator is symmetric, and an asymmetric comparator can’t be\n  // used to test whether a single value is comparable.\n  if (f.length !== 2) {\n    compare1 = ascending;\n    compare2 = (d, x) => ascending(f(d), x);\n    delta = (d, x) => f(d) - x;\n  } else {\n    compare1 = f === ascending || f === descending ? f : zero;\n    compare2 = f;\n    delta = f;\n  }\n  function left(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = lo + hi >>> 1;\n        if (compare2(a[mid], x) < 0) lo = mid + 1;else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n  function right(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = lo + hi >>> 1;\n        if (compare2(a[mid], x) <= 0) lo = mid + 1;else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n  function center(a, x, lo = 0, hi = a.length) {\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n  return {\n    left,\n    center,\n    right\n  };\n}\nfunction zero() {\n  return 0;\n}", "map": {"version": 3, "names": ["ascending", "descending", "bisector", "f", "compare1", "compare2", "delta", "length", "d", "x", "zero", "left", "a", "lo", "hi", "mid", "right", "center", "i"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/d3-array/src/bisector.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\nimport descending from \"./descending.js\";\n\nexport default function bisector(f) {\n  let compare1, compare2, delta;\n\n  // If an accessor is specified, promote it to a comparator. In this case we\n  // can test whether the search value is (self-) comparable. We can’t do this\n  // for a comparator (except for specific, known comparators) because we can’t\n  // tell if the comparator is symmetric, and an asymmetric comparator can’t be\n  // used to test whether a single value is comparable.\n  if (f.length !== 2) {\n    compare1 = ascending;\n    compare2 = (d, x) => ascending(f(d), x);\n    delta = (d, x) => f(d) - x;\n  } else {\n    compare1 = f === ascending || f === descending ? f : zero;\n    compare2 = f;\n    delta = f;\n  }\n\n  function left(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) < 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function right(a, x, lo = 0, hi = a.length) {\n    if (lo < hi) {\n      if (compare1(x, x) !== 0) return hi;\n      do {\n        const mid = (lo + hi) >>> 1;\n        if (compare2(a[mid], x) <= 0) lo = mid + 1;\n        else hi = mid;\n      } while (lo < hi);\n    }\n    return lo;\n  }\n\n  function center(a, x, lo = 0, hi = a.length) {\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n\n  return {left, center, right};\n}\n\nfunction zero() {\n  return 0;\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,OAAOC,UAAU,MAAM,iBAAiB;AAExC,eAAe,SAASC,QAAQA,CAACC,CAAC,EAAE;EAClC,IAAIC,QAAQ,EAAEC,QAAQ,EAAEC,KAAK;;EAE7B;EACA;EACA;EACA;EACA;EACA,IAAIH,CAAC,CAACI,MAAM,KAAK,CAAC,EAAE;IAClBH,QAAQ,GAAGJ,SAAS;IACpBK,QAAQ,GAAGA,CAACG,CAAC,EAAEC,CAAC,KAAKT,SAAS,CAACG,CAAC,CAACK,CAAC,CAAC,EAAEC,CAAC,CAAC;IACvCH,KAAK,GAAGA,CAACE,CAAC,EAAEC,CAAC,KAAKN,CAAC,CAACK,CAAC,CAAC,GAAGC,CAAC;EAC5B,CAAC,MAAM;IACLL,QAAQ,GAAGD,CAAC,KAAKH,SAAS,IAAIG,CAAC,KAAKF,UAAU,GAAGE,CAAC,GAAGO,IAAI;IACzDL,QAAQ,GAAGF,CAAC;IACZG,KAAK,GAAGH,CAAC;EACX;EAEA,SAASQ,IAAIA,CAACC,CAAC,EAAEH,CAAC,EAAEI,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAGF,CAAC,CAACL,MAAM,EAAE;IACzC,IAAIM,EAAE,GAAGC,EAAE,EAAE;MACX,IAAIV,QAAQ,CAACK,CAAC,EAAEA,CAAC,CAAC,KAAK,CAAC,EAAE,OAAOK,EAAE;MACnC,GAAG;QACD,MAAMC,GAAG,GAAIF,EAAE,GAAGC,EAAE,KAAM,CAAC;QAC3B,IAAIT,QAAQ,CAACO,CAAC,CAACG,GAAG,CAAC,EAAEN,CAAC,CAAC,GAAG,CAAC,EAAEI,EAAE,GAAGE,GAAG,GAAG,CAAC,CAAC,KACrCD,EAAE,GAAGC,GAAG;MACf,CAAC,QAAQF,EAAE,GAAGC,EAAE;IAClB;IACA,OAAOD,EAAE;EACX;EAEA,SAASG,KAAKA,CAACJ,CAAC,EAAEH,CAAC,EAAEI,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAGF,CAAC,CAACL,MAAM,EAAE;IAC1C,IAAIM,EAAE,GAAGC,EAAE,EAAE;MACX,IAAIV,QAAQ,CAACK,CAAC,EAAEA,CAAC,CAAC,KAAK,CAAC,EAAE,OAAOK,EAAE;MACnC,GAAG;QACD,MAAMC,GAAG,GAAIF,EAAE,GAAGC,EAAE,KAAM,CAAC;QAC3B,IAAIT,QAAQ,CAACO,CAAC,CAACG,GAAG,CAAC,EAAEN,CAAC,CAAC,IAAI,CAAC,EAAEI,EAAE,GAAGE,GAAG,GAAG,CAAC,CAAC,KACtCD,EAAE,GAAGC,GAAG;MACf,CAAC,QAAQF,EAAE,GAAGC,EAAE;IAClB;IACA,OAAOD,EAAE;EACX;EAEA,SAASI,MAAMA,CAACL,CAAC,EAAEH,CAAC,EAAEI,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAGF,CAAC,CAACL,MAAM,EAAE;IAC3C,MAAMW,CAAC,GAAGP,IAAI,CAACC,CAAC,EAAEH,CAAC,EAAEI,EAAE,EAAEC,EAAE,GAAG,CAAC,CAAC;IAChC,OAAOI,CAAC,GAAGL,EAAE,IAAIP,KAAK,CAACM,CAAC,CAACM,CAAC,GAAG,CAAC,CAAC,EAAET,CAAC,CAAC,GAAG,CAACH,KAAK,CAACM,CAAC,CAACM,CAAC,CAAC,EAAET,CAAC,CAAC,GAAGS,CAAC,GAAG,CAAC,GAAGA,CAAC;EACnE;EAEA,OAAO;IAACP,IAAI;IAAEM,MAAM;IAAED;EAAK,CAAC;AAC9B;AAEA,SAASN,IAAIA,CAAA,EAAG;EACd,OAAO,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}