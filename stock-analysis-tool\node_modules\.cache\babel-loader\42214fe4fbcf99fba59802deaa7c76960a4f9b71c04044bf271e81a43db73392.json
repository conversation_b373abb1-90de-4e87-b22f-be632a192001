{"ast": null, "code": "var freeGlobal = require('./_freeGlobal');\n\n/** Detect free variable `exports`. */\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n\n/** Detect free variable `module`. */\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n\n/** Detect the popular CommonJS extension `module.exports`. */\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n\n/** Detect free variable `process` from Node.js. */\nvar freeProcess = moduleExports && freeGlobal.process;\n\n/** Used to access faster Node.js helpers. */\nvar nodeUtil = function () {\n  try {\n    // Use `util.types` for Node.js 10+.\n    var types = freeModule && freeModule.require && freeModule.require('util').types;\n    if (types) {\n      return types;\n    }\n\n    // Legacy `process.binding('util')` for Node.js < 10.\n    return freeProcess && freeProcess.binding && freeProcess.binding('util');\n  } catch (e) {}\n}();\nmodule.exports = nodeUtil;", "map": {"version": 3, "names": ["freeGlobal", "require", "freeExports", "exports", "nodeType", "freeModule", "module", "moduleExports", "freeProcess", "process", "nodeUtil", "types", "binding", "e"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/lodash/_nodeUtil.js"], "sourcesContent": ["var freeGlobal = require('./_freeGlobal');\n\n/** Detect free variable `exports`. */\nvar freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n\n/** Detect free variable `module`. */\nvar freeModule = freeExports && typeof module == 'object' && module && !module.nodeType && module;\n\n/** Detect the popular CommonJS extension `module.exports`. */\nvar moduleExports = freeModule && freeModule.exports === freeExports;\n\n/** Detect free variable `process` from Node.js. */\nvar freeProcess = moduleExports && freeGlobal.process;\n\n/** Used to access faster Node.js helpers. */\nvar nodeUtil = (function() {\n  try {\n    // Use `util.types` for Node.js 10+.\n    var types = freeModule && freeModule.require && freeModule.require('util').types;\n\n    if (types) {\n      return types;\n    }\n\n    // Legacy `process.binding('util')` for Node.js < 10.\n    return freeProcess && freeProcess.binding && freeProcess.binding('util');\n  } catch (e) {}\n}());\n\nmodule.exports = nodeUtil;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAEzC;AACA,IAAIC,WAAW,GAAG,OAAOC,OAAO,IAAI,QAAQ,IAAIA,OAAO,IAAI,CAACA,OAAO,CAACC,QAAQ,IAAID,OAAO;;AAEvF;AACA,IAAIE,UAAU,GAAGH,WAAW,IAAI,OAAOI,MAAM,IAAI,QAAQ,IAAIA,MAAM,IAAI,CAACA,MAAM,CAACF,QAAQ,IAAIE,MAAM;;AAEjG;AACA,IAAIC,aAAa,GAAGF,UAAU,IAAIA,UAAU,CAACF,OAAO,KAAKD,WAAW;;AAEpE;AACA,IAAIM,WAAW,GAAGD,aAAa,IAAIP,UAAU,CAACS,OAAO;;AAErD;AACA,IAAIC,QAAQ,GAAI,YAAW;EACzB,IAAI;IACF;IACA,IAAIC,KAAK,GAAGN,UAAU,IAAIA,UAAU,CAACJ,OAAO,IAAII,UAAU,CAACJ,OAAO,CAAC,MAAM,CAAC,CAACU,KAAK;IAEhF,IAAIA,KAAK,EAAE;MACT,OAAOA,KAAK;IACd;;IAEA;IACA,OAAOH,WAAW,IAAIA,WAAW,CAACI,OAAO,IAAIJ,WAAW,CAACI,OAAO,CAAC,MAAM,CAAC;EAC1E,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;AACf,CAAC,CAAC,CAAE;AAEJP,MAAM,CAACH,OAAO,GAAGO,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}