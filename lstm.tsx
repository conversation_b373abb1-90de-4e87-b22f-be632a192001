import React, { useState, useEffect, useRef } from 'react';
import { Search, TrendingUp, TrendingDown, AlertTriangle, Calculator, Brain, BarChart3, Activity, DollarSign, Target, Zap, Star, ArrowUp, ArrowDown, RefreshCw } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, BarChart, Bar, AreaChart, Area, CandlestickChart } from 'recharts';

const StockAnalysisTool = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStock, setSelectedStock] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(false);
  const [predictions, setPredictions] = useState(null);
  const [analysisResults, setAnalysisResults] = useState(null);

  // 模拟股票数据
  const mockStockData = {
    'AAPL': {
      symbol: 'AAPL',
      name: '苹果公司',
      price: 185.75,
      change: 2.35,
      changePercent: 1.28,
      volume: 45680000,
      marketCap: 2890000000000,
      pe: 28.5,
      eps: 6.52,
      dividend: 0.96,
      beta: 1.25,
      high52: 198.23,
      low52: 124.17,
      avgVolume: 52000000,
      sector: '科技',
      industry: '消费电子',
      employees: 164000
    },
    'TSLA': {
      symbol: 'TSLA',
      name: '特斯拉',
      price: 248.50,
      change: -8.45,
      changePercent: -3.29,
      volume: 98750000,
      marketCap: 789000000000,
      pe: 65.2,
      eps: 3.81,
      dividend: 0,
      beta: 2.15,
      high52: 299.29,
      low52: 138.80,
      avgVolume: 75000000,
      sector: '汽车',
      industry: '电动汽车',
      employees: 140000
    },
    'MSFT': {
      symbol: 'MSFT',
      name: '微软',
      price: 378.85,
      change: 5.20,
      changePercent: 1.39,
      volume: 28450000,
      marketCap: 2810000000000,
      pe: 32.1,
      eps: 11.80,
      dividend: 2.72,
      beta: 0.89,
      high52: 384.30,
      low52: 309.45,
      avgVolume: 32000000,
      sector: '科技',
      industry: '软件',
      employees: 221000
    },
    'GOOGL': {
      symbol: 'GOOGL',
      name: '谷歌',
      price: 141.80,
      change: -1.25,
      changePercent: -0.87,
      volume: 35670000,
      marketCap: 1750000000000,
      pe: 24.3,
      eps: 5.83,
      dividend: 0,
      beta: 1.05,
      high52: 151.55,
      low52: 121.46,
      avgVolume: 38000000,
      sector: '科技',
      industry: '互联网',
      employees: 174000
    },
    'AMZN': {
      symbol: 'AMZN',
      name: '亚马逊',
      price: 156.77,
      change: 3.12,
      changePercent: 2.03,
      volume: 42180000,
      marketCap: 1620000000000,
      pe: 47.8,
      eps: 3.28,
      dividend: 0,
      beta: 1.33,
      high52: 170.10,
      low52: 118.35,
      avgVolume: 45000000,
      sector: '消费',
      industry: '电商',
      employees: 1540000
    }
  };

  // 模拟历史价格数据
  const generateHistoricalData = () => {
    const data = [];
    let price = selectedStock?.price || 150;
    for (let i = 30; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      price += (Math.random() - 0.5) * 10;
      data.push({
        date: date.toISOString().split('T')[0],
        price: parseFloat(price.toFixed(2)),
        volume: Math.floor(Math.random() * 100000000) + 20000000,
        high: parseFloat((price + Math.random() * 5).toFixed(2)),
        low: parseFloat((price - Math.random() * 5).toFixed(2)),
        open: parseFloat((price + (Math.random() - 0.5) * 3).toFixed(2)),
        close: parseFloat(price.toFixed(2))
      });
    }
    return data;
  };

  // LSTM预测功能
  const runLSTMPrediction = async () => {
    setIsLoading(true);
    // 模拟LSTM预测过程
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const currentPrice = selectedStock?.price || 150;
    const predictions = [];
    let predictedPrice = currentPrice;
    
    for (let i = 1; i <= 10; i++) {
      predictedPrice += (Math.random() - 0.45) * 5; // 略微向上偏向
      predictions.push({
        day: i,
        predictedPrice: parseFloat(predictedPrice.toFixed(2)),
        confidence: Math.max(0.6, 0.95 - i * 0.05)
      });
    }
    
    setPredictions(predictions);
    setIsLoading(false);
  };

  // 综合分析
  const performAnalysis = () => {
    if (!selectedStock) return;
    
    const stock = selectedStock;
    const historicalData = generateHistoricalData();
    
    // 技术指标计算
    const sma20 = historicalData.slice(-20).reduce((sum, d) => sum + d.price, 0) / 20;
    const sma50 = historicalData.slice(-50)?.reduce((sum, d) => sum + d.price, 0) / Math.min(50, historicalData.length) || sma20;
    
    // RSI计算
    const rsi = 45 + Math.random() * 20; // 模拟RSI
    
    // MACD
    const macd = Math.random() * 2 - 1;
    
    // 估值分析
    const peRating = stock.pe < 15 ? '低估' : stock.pe > 30 ? '高估' : '合理';
    const pbRatio = 1.5 + Math.random() * 3;
    
    // 买卖建议
    let recommendation = '持有';
    let buyPrice = stock.price * 0.95;
    let sellPrice = stock.price * 1.15;
    
    if (stock.changePercent > 2 && rsi < 70) {
      recommendation = '买入';
      buyPrice = stock.price * 1.02;
    } else if (stock.changePercent < -2 && rsi > 30) {
      recommendation = '买入';
      buyPrice = stock.price * 0.98;
    } else if (rsi > 75) {
      recommendation = '卖出';
      sellPrice = stock.price * 0.98;
    }
    
    setAnalysisResults({
      technicalIndicators: {
        sma20: parseFloat(sma20.toFixed(2)),
        sma50: parseFloat(sma50.toFixed(2)),
        rsi: parseFloat(rsi.toFixed(2)),
        macd: parseFloat(macd.toFixed(3))
      },
      valuation: {
        peRating,
        pbRatio: parseFloat(pbRatio.toFixed(2)),
        divYield: stock.dividend / stock.price * 100
      },
      recommendation: {
        action: recommendation,
        buyPrice: parseFloat(buyPrice.toFixed(2)),
        sellPrice: parseFloat(sellPrice.toFixed(2)),
        targetPrice: parseFloat((stock.price * (1.1 + Math.random() * 0.2)).toFixed(2))
      },
      riskLevel: stock.beta > 1.5 ? '高风险' : stock.beta < 0.8 ? '低风险' : '中等风险'
    });
  };

  const searchStock = (term) => {
    console.log('搜索股票:', term); // 调试日志
    const upperTerm = term.toUpperCase();
    let found = null;
    
    // 精确匹配股票代码
    if (mockStockData[upperTerm]) {
      found = mockStockData[upperTerm];
    } else {
      // 模糊匹配公司名称
      found = Object.values(mockStockData).find(
        stock => stock.name.includes(term) || 
                 stock.name.toLowerCase().includes(term.toLowerCase()) ||
                 stock.symbol.toLowerCase().includes(term.toLowerCase())
      );
    }
    
    if (found) {
      console.log('找到股票:', found); // 调试日志
      setSelectedStock(found);
      setActiveTab('overview'); // 重置到概览页面
    } else {
      alert(`未找到股票: ${term}\n\n可搜索的股票:\n- AAPL (苹果公司)\n- TSLA (特斯拉)\n- MSFT (微软)\n- GOOGL (谷歌)\n- AMZN (亚马逊)`);
    }
  };

  const handleSearch = () => {
    console.log('开始搜索:', searchTerm); // 调试日志
    if (searchTerm.trim()) {
      searchStock(searchTerm.trim());
    } else {
      alert('请输入股票代码或公司名称');
    }
  };

  useEffect(() => {
    if (selectedStock) {
      performAnalysis();
    }
  }, [selectedStock]);

  const StockCard = ({ stock }) => (
    <div className="bg-gradient-to-br from-white to-gray-50 rounded-xl p-6 shadow-lg border border-gray-200">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">{stock.name}</h2>
          <p className="text-gray-600">{stock.symbol}</p>
        </div>
        <div className="text-right">
          <div className="text-3xl font-bold text-gray-900">${stock.price}</div>
          <div className={`flex items-center ${stock.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {stock.changePercent >= 0 ? <ArrowUp className="w-4 h-4 mr-1" /> : <ArrowDown className="w-4 h-4 mr-1" />}
            ${Math.abs(stock.change)} ({Math.abs(stock.changePercent)}%)
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div>
          <div className="text-gray-500">市值</div>
          <div className="font-semibold">${(stock.marketCap / 1e9).toFixed(1)}B</div>
        </div>
        <div>
          <div className="text-gray-500">P/E比率</div>
          <div className="font-semibold">{stock.pe}</div>
        </div>
        <div>
          <div className="text-gray-500">成交量</div>
          <div className="font-semibold">{(stock.volume / 1e6).toFixed(1)}M</div>
        </div>
        <div>
          <div className="text-gray-500">Beta</div>
          <div className="font-semibold">{stock.beta}</div>
        </div>
      </div>
    </div>
  );

  const TechnicalAnalysis = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-xl p-6 shadow-lg">
        <h3 className="text-xl font-bold mb-4 flex items-center">
          <Activity className="w-5 h-5 mr-2 text-blue-600" />
          技术指标
        </h3>
        {analysisResults && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="text-sm text-gray-600">SMA20</div>
              <div className="text-lg font-bold">${analysisResults.technicalIndicators.sma20}</div>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <div className="text-sm text-gray-600">SMA50</div>
              <div className="text-lg font-bold">${analysisResults.technicalIndicators.sma50}</div>
            </div>
            <div className="p-4 bg-yellow-50 rounded-lg">
              <div className="text-sm text-gray-600">RSI</div>
              <div className="text-lg font-bold">{analysisResults.technicalIndicators.rsi}</div>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <div className="text-sm text-gray-600">MACD</div>
              <div className="text-lg font-bold">{analysisResults.technicalIndicators.macd}</div>
            </div>
          </div>
        )}
      </div>

      <div className="bg-white rounded-xl p-6 shadow-lg">
        <h3 className="text-xl font-bold mb-4">价格走势图</h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={generateHistoricalData()}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Line type="monotone" dataKey="price" stroke="#3B82F6" strokeWidth={2} />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );

  const PredictionView = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-xl p-6 shadow-lg">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-bold flex items-center">
            <Brain className="w-5 h-5 mr-2 text-purple-600" />
            LSTM预测分析
          </h3>
          <button
            onClick={runLSTMPrediction}
            disabled={isLoading}
            className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:opacity-50 flex items-center"
          >
            {isLoading ? <RefreshCw className="w-4 h-4 mr-2 animate-spin" /> : <Zap className="w-4 h-4 mr-2" />}
            {isLoading ? '分析中...' : '运行预测'}
          </button>
        </div>
        
        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
              <p className="text-gray-600">正在运行LSTM神经网络预测...</p>
            </div>
          </div>
        )}
        
        {predictions && !isLoading && (
          <div>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={predictions}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="predictedPrice" stroke="#8B5CF6" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
            <div className="mt-4 grid grid-cols-2 md:grid-cols-5 gap-4">
              {predictions.slice(0, 5).map((pred, idx) => (
                <div key={idx} className="p-3 bg-purple-50 rounded-lg text-center">
                  <div className="text-sm text-gray-600">第{pred.day}天</div>
                  <div className="font-bold">${pred.predictedPrice}</div>
                  <div className="text-xs text-green-600">置信度: {(pred.confidence * 100).toFixed(1)}%</div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const RecommendationView = () => (
    <div className="space-y-6">
      {analysisResults && (
        <>
          <div className="bg-white rounded-xl p-6 shadow-lg">
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <Target className="w-5 h-5 mr-2 text-green-600" />
              投资建议
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-4">
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  analysisResults.recommendation.action === '买入' ? 'bg-green-100 text-green-800' :
                  analysisResults.recommendation.action === '卖出' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {analysisResults.recommendation.action}
                </div>
                <div className="mt-2 text-sm text-gray-600">建议操作</div>
              </div>
              <div className="text-center p-4">
                <div className="text-xl font-bold text-green-600">${analysisResults.recommendation.buyPrice}</div>
                <div className="text-sm text-gray-600">建议买入价</div>
              </div>
              <div className="text-center p-4">
                <div className="text-xl font-bold text-red-600">${analysisResults.recommendation.sellPrice}</div>
                <div className="text-sm text-gray-600">建议卖出价</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-lg">
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <Calculator className="w-5 h-5 mr-2 text-blue-600" />
              估值分析
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="p-4 border rounded-lg">
                <div className="text-sm text-gray-600">P/E估值</div>
                <div className={`text-lg font-bold ${
                  analysisResults.valuation.peRating === '低估' ? 'text-green-600' :
                  analysisResults.valuation.peRating === '高估' ? 'text-red-600' : 'text-yellow-600'
                }`}>
                  {analysisResults.valuation.peRating}
                </div>
              </div>
              <div className="p-4 border rounded-lg">
                <div className="text-sm text-gray-600">P/B比率</div>
                <div className="text-lg font-bold">{analysisResults.valuation.pbRatio}</div>
              </div>
              <div className="p-4 border rounded-lg">
                <div className="text-sm text-gray-600">股息收益率</div>
                <div className="text-lg font-bold">{analysisResults.valuation.divYield.toFixed(2)}%</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-lg">
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2 text-orange-600" />
              风险评估
            </h3>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-lg font-semibold">{analysisResults.riskLevel}</div>
                <div className="text-sm text-gray-600">基于Beta系数和市场波动性</div>
              </div>
              <div className={`px-4 py-2 rounded-full text-sm font-medium ${
                analysisResults.riskLevel === '低风险' ? 'bg-green-100 text-green-800' :
                analysisResults.riskLevel === '高风险' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
              }`}>
                {analysisResults.riskLevel}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );

  const tabs = [
    { id: 'overview', name: '概览', icon: BarChart3 },
    { id: 'technical', name: '技术分析', icon: Activity },
    { id: 'prediction', name: 'AI预测', icon: Brain },
    { id: 'recommendation', name: '投资建议', icon: Target }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            专业股票分析工具
          </h1>
          <p className="text-gray-600">基于LSTM深度学习的智能股票分析与预测系统</p>
        </div>

        {/* Search */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                placeholder="输入股票代码或公司名称 (如: AAPL, 苹果公司, TSLA, 特斯拉)"
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <button
              onClick={handleSearch}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
            >
              <Search className="w-5 h-5 mr-2" />
              搜索
            </button>
          </div>
          
          <div className="mt-4 flex flex-wrap gap-2">
            <span className="text-sm text-gray-600">可搜索股票:</span>
            {Object.values(mockStockData).map(stock => (
              <button
                key={stock.symbol}
                onClick={() => {
                  setSearchTerm(stock.symbol);
                  searchStock(stock.symbol);
                }}
                className="bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm transition-colors"
              >
                {stock.symbol} - {stock.name}
              </button>
            ))}
          </div>
        </div>

        {/* Main Content */}
        {selectedStock && (
          <>
            <StockCard stock={selectedStock} />
            
            {/* Tabs */}
            <div className="mt-8">
              <div className="border-b border-gray-200 bg-white rounded-t-xl">
                <nav className="flex space-x-8 px-6">
                  {tabs.map((tab) => {
                    const Icon = tab.icon;
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                          activeTab === tab.id
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        <Icon className="w-4 h-4 mr-2" />
                        {tab.name}
                      </button>
                    );
                  })}
                </nav>
              </div>
              
              <div className="bg-gray-50 p-6 rounded-b-xl">
                {activeTab === 'overview' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-white rounded-xl p-6 shadow-lg">
                      <h3 className="text-xl font-bold mb-4">基本信息</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-600">行业:</span>
                          <span className="font-semibold">{selectedStock.industry}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">板块:</span>
                          <span className="font-semibold">{selectedStock.sector}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">员工数:</span>
                          <span className="font-semibold">{selectedStock.employees.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">52周最高:</span>
                          <span className="font-semibold">${selectedStock.high52}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">52周最低:</span>
                          <span className="font-semibold">${selectedStock.low52}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white rounded-xl p-6 shadow-lg">
                      <h3 className="text-xl font-bold mb-4">成交量分析</h3>
                      <ResponsiveContainer width="100%" height={200}>
                        <BarChart data={generateHistoricalData().slice(-7)}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="date" />
                          <YAxis />
                          <Tooltip />
                          <Bar dataKey="volume" fill="#3B82F6" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                )}
                
                {activeTab === 'technical' && <TechnicalAnalysis />}
                {activeTab === 'prediction' && <PredictionView />}
                {activeTab === 'recommendation' && <RecommendationView />}
              </div>
            </div>
          </>
        )}
        
        {!selectedStock && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📈</div>
            <h3 className="text-xl font-semibold text-gray-600 mb-2">开始分析股票</h3>
            <p className="text-gray-500 mb-4">在上方搜索框中输入股票代码或公司名称开始分析</p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-w-md mx-auto">
              <h4 className="font-semibold text-yellow-800 mb-2">💡 使用提示:</h4>
              <div className="text-sm text-yellow-700 text-left">
                <p>• 输入股票代码: AAPL, TSLA, MSFT 等</p>
                <p>• 输入公司名称: 苹果, 特斯拉, 微软 等</p>
                <p>• 点击下方快捷按钮直接选择</p>
                <p>• 支持按回车键快速搜索</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StockAnalysisTool;