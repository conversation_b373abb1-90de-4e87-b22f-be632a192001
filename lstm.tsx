import React, { useState, useEffect, useRef } from 'react';
import { Search, TrendingUp, TrendingDown, AlertTriangle, Calculator, Brain, BarChart3, Activity, DollarSign, Target, Zap, Star, ArrowUp, ArrowDown, RefreshCw } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, BarChart, Bar, AreaChart, Area, CandlestickChart } from 'recharts';

const StockAnalysisTool = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStock, setSelectedStock] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(false);
  const [predictions, setPredictions] = useState(null);
  const [analysisResults, setAnalysisResults] = useState(null);
  const [searchSuggestions, setSearchSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // 扩展的股票数据库
  const mockStockData = {
    'AAPL': {
      symbol: 'AAPL',
      name: '苹果公司',
      aliases: ['苹果', 'Apple', 'iPhone', '库克', 'iOS'],
      price: 185.75,
      change: 2.35,
      changePercent: 1.28,
      volume: 45680000,
      marketCap: 2890000000000,
      pe: 28.5,
      eps: 6.52,
      dividend: 0.96,
      beta: 1.25,
      high52: 198.23,
      low52: 124.17,
      avgVolume: 52000000,
      sector: '科技',
      industry: '消费电子',
      employees: 164000
    },
    'TSLA': {
      symbol: 'TSLA',
      name: '特斯拉',
      aliases: ['特斯拉', 'Tesla', '马斯克', '电动车', '新能源汽车', 'Model'],
      price: 248.50,
      change: -8.45,
      changePercent: -3.29,
      volume: 98750000,
      marketCap: 789000000000,
      pe: 65.2,
      eps: 3.81,
      dividend: 0,
      beta: 2.15,
      high52: 299.29,
      low52: 138.80,
      avgVolume: 75000000,
      sector: '汽车',
      industry: '电动汽车',
      employees: 140000
    },
    'MSFT': {
      symbol: 'MSFT',
      name: '微软',
      aliases: ['微软', 'Microsoft', 'Windows', 'Office', 'Azure', '比尔盖茨'],
      price: 378.85,
      change: 5.20,
      changePercent: 1.39,
      volume: 28450000,
      marketCap: 2810000000000,
      pe: 32.1,
      eps: 11.80,
      dividend: 2.72,
      beta: 0.89,
      high52: 384.30,
      low52: 309.45,
      avgVolume: 32000000,
      sector: '科技',
      industry: '软件',
      employees: 221000
    },
    'GOOGL': {
      symbol: 'GOOGL',
      name: '谷歌',
      aliases: ['谷歌', 'Google', 'Alphabet', '搜索', 'Android', 'YouTube'],
      price: 141.80,
      change: -1.25,
      changePercent: -0.87,
      volume: 35670000,
      marketCap: 1750000000000,
      pe: 24.3,
      eps: 5.83,
      dividend: 0,
      beta: 1.05,
      high52: 151.55,
      low52: 121.46,
      avgVolume: 38000000,
      sector: '科技',
      industry: '互联网',
      employees: 174000
    },
    'AMZN': {
      symbol: 'AMZN',
      name: '亚马逊',
      aliases: ['亚马逊', 'Amazon', '贝索斯', '电商', 'AWS', '云计算'],
      price: 156.77,
      change: 3.12,
      changePercent: 2.03,
      volume: 42180000,
      marketCap: 1620000000000,
      pe: 47.8,
      eps: 3.28,
      dividend: 0,
      beta: 1.33,
      high52: 170.10,
      low52: 118.35,
      avgVolume: 45000000,
      sector: '消费',
      industry: '电商',
      employees: 1540000
    },
    'NVDA': {
      symbol: 'NVDA',
      name: '英伟达',
      aliases: ['英伟达', 'NVIDIA', '显卡', 'GPU', 'AI芯片', '人工智能'],
      price: 485.20,
      change: 12.45,
      changePercent: 2.63,
      volume: 52340000,
      marketCap: 1200000000000,
      pe: 68.5,
      eps: 7.08,
      dividend: 0.16,
      beta: 1.68,
      high52: 502.66,
      low52: 180.96,
      avgVolume: 48000000,
      sector: '科技',
      industry: '半导体',
      employees: 29600
    },
    'META': {
      symbol: 'META',
      name: 'Meta平台',
      aliases: ['Meta', 'Facebook', 'FB', '脸书', 'Instagram', 'WhatsApp', '扎克伯格'],
      price: 325.40,
      change: -4.20,
      changePercent: -1.27,
      volume: 18750000,
      marketCap: 825000000000,
      pe: 23.8,
      eps: 13.67,
      dividend: 0,
      beta: 1.15,
      high52: 384.33,
      low52: 274.39,
      avgVolume: 22000000,
      sector: '科技',
      industry: '社交媒体',
      employees: 77805
    },
    'NFLX': {
      symbol: 'NFLX',
      name: '奈飞',
      aliases: ['奈飞', 'Netflix', '网飞', '流媒体', '视频'],
      price: 445.75,
      change: 8.90,
      changePercent: 2.04,
      volume: 3420000,
      marketCap: 198000000000,
      pe: 44.2,
      eps: 10.09,
      dividend: 0,
      beta: 1.23,
      high52: 485.73,
      low52: 344.73,
      avgVolume: 4200000,
      sector: '通信',
      industry: '流媒体',
      employees: 13000
    },
    'BABA': {
      symbol: 'BABA',
      name: '阿里巴巴',
      aliases: ['阿里巴巴', 'Alibaba', '阿里', '马云', '淘宝', '天猫'],
      price: 78.45,
      change: 1.25,
      changePercent: 1.62,
      volume: 15680000,
      marketCap: 189000000000,
      pe: 18.5,
      eps: 4.24,
      dividend: 0,
      beta: 0.78,
      high52: 118.01,
      low52: 66.63,
      avgVolume: 18000000,
      sector: '消费',
      industry: '电商',
      employees: 245700
    },
    'TCEHY': {
      symbol: 'TCEHY',
      name: '腾讯控股',
      aliases: ['腾讯', 'Tencent', '微信', 'QQ', '游戏', '马化腾'],
      price: 42.80,
      change: 0.65,
      changePercent: 1.54,
      volume: 8950000,
      marketCap: 410000000000,
      pe: 16.8,
      eps: 2.55,
      dividend: 0.54,
      beta: 0.85,
      high52: 56.78,
      low52: 32.45,
      avgVolume: 12000000,
      sector: '科技',
      industry: '互联网',
      employees: 116213
    }
  };

  // 模拟历史价格数据
  const generateHistoricalData = () => {
    const data = [];
    let price = selectedStock?.price || 150;
    for (let i = 30; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      price += (Math.random() - 0.5) * 10;
      data.push({
        date: date.toISOString().split('T')[0],
        price: parseFloat(price.toFixed(2)),
        volume: Math.floor(Math.random() * 100000000) + 20000000,
        high: parseFloat((price + Math.random() * 5).toFixed(2)),
        low: parseFloat((price - Math.random() * 5).toFixed(2)),
        open: parseFloat((price + (Math.random() - 0.5) * 3).toFixed(2)),
        close: parseFloat(price.toFixed(2))
      });
    }
    return data;
  };

  // LSTM预测功能
  const runLSTMPrediction = async () => {
    setIsLoading(true);
    // 模拟LSTM预测过程
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const currentPrice = selectedStock?.price || 150;
    const predictions = [];
    let predictedPrice = currentPrice;
    
    for (let i = 1; i <= 10; i++) {
      predictedPrice += (Math.random() - 0.45) * 5; // 略微向上偏向
      predictions.push({
        day: i,
        predictedPrice: parseFloat(predictedPrice.toFixed(2)),
        confidence: Math.max(0.6, 0.95 - i * 0.05)
      });
    }
    
    setPredictions(predictions);
    setIsLoading(false);
  };

  // 综合分析
  const performAnalysis = () => {
    if (!selectedStock) return;
    
    const stock = selectedStock;
    const historicalData = generateHistoricalData();
    
    // 技术指标计算
    const sma20 = historicalData.slice(-20).reduce((sum, d) => sum + d.price, 0) / 20;
    const sma50 = historicalData.slice(-50)?.reduce((sum, d) => sum + d.price, 0) / Math.min(50, historicalData.length) || sma20;
    
    // RSI计算
    const rsi = 45 + Math.random() * 20; // 模拟RSI
    
    // MACD
    const macd = Math.random() * 2 - 1;
    
    // 估值分析
    const peRating = stock.pe < 15 ? '低估' : stock.pe > 30 ? '高估' : '合理';
    const pbRatio = 1.5 + Math.random() * 3;
    
    // 买卖建议
    let recommendation = '持有';
    let buyPrice = stock.price * 0.95;
    let sellPrice = stock.price * 1.15;
    
    if (stock.changePercent > 2 && rsi < 70) {
      recommendation = '买入';
      buyPrice = stock.price * 1.02;
    } else if (stock.changePercent < -2 && rsi > 30) {
      recommendation = '买入';
      buyPrice = stock.price * 0.98;
    } else if (rsi > 75) {
      recommendation = '卖出';
      sellPrice = stock.price * 0.98;
    }
    
    setAnalysisResults({
      technicalIndicators: {
        sma20: parseFloat(sma20.toFixed(2)),
        sma50: parseFloat(sma50.toFixed(2)),
        rsi: parseFloat(rsi.toFixed(2)),
        macd: parseFloat(macd.toFixed(3))
      },
      valuation: {
        peRating,
        pbRatio: parseFloat(pbRatio.toFixed(2)),
        divYield: stock.dividend / stock.price * 100
      },
      recommendation: {
        action: recommendation,
        buyPrice: parseFloat(buyPrice.toFixed(2)),
        sellPrice: parseFloat(sellPrice.toFixed(2)),
        targetPrice: parseFloat((stock.price * (1.1 + Math.random() * 0.2)).toFixed(2))
      },
      riskLevel: stock.beta > 1.5 ? '高风险' : stock.beta < 0.8 ? '低风险' : '中等风险'
    });
  };

  // 智能搜索算法
  const searchStock = (term) => {
    console.log('搜索股票:', term);
    if (!term || !term.trim()) return null;

    const searchTerm = term.trim();
    const upperTerm = searchTerm.toUpperCase();
    const lowerTerm = searchTerm.toLowerCase();

    let found = null;
    let bestMatch = null;
    let maxScore = 0;

    // 1. 精确匹配股票代码
    if (mockStockData[upperTerm]) {
      found = mockStockData[upperTerm];
      console.log('精确匹配股票代码:', found);
    } else {
      // 2. 智能模糊匹配
      Object.values(mockStockData).forEach(stock => {
        let score = 0;

        // 股票代码匹配 (权重最高)
        if (stock.symbol.toLowerCase().includes(lowerTerm)) {
          score += 100;
        }

        // 公司名称完全匹配
        if (stock.name === searchTerm) {
          score += 90;
        }

        // 公司名称包含匹配
        if (stock.name.includes(searchTerm)) {
          score += 80;
        }

        // 公司名称部分匹配
        if (stock.name.toLowerCase().includes(lowerTerm)) {
          score += 60;
        }

        // 别名匹配
        stock.aliases.forEach(alias => {
          if (alias === searchTerm) {
            score += 85; // 别名完全匹配
          } else if (alias.toLowerCase() === lowerTerm) {
            score += 75; // 别名忽略大小写匹配
          } else if (alias.toLowerCase().includes(lowerTerm)) {
            score += 50; // 别名包含匹配
          }
        });

        // 行业匹配
        if (stock.industry.includes(searchTerm) || stock.sector.includes(searchTerm)) {
          score += 30;
        }

        // 模糊匹配 - 检查是否包含搜索词的字符
        const nameChars = stock.name.toLowerCase();
        const termChars = lowerTerm;
        let charMatches = 0;
        for (let char of termChars) {
          if (nameChars.includes(char)) {
            charMatches++;
          }
        }
        if (charMatches > 0) {
          score += (charMatches / termChars.length) * 20;
        }

        if (score > maxScore) {
          maxScore = score;
          bestMatch = stock;
        }
      });

      // 设置最低匹配阈值
      if (maxScore >= 30) {
        found = bestMatch;
        console.log('模糊匹配找到股票:', found, '匹配分数:', maxScore);
      }
    }

    if (found) {
      setSelectedStock(found);
      setActiveTab('overview');
      return found;
    } else {
      // 生成搜索建议
      const suggestions = generateSearchSuggestions(searchTerm);
      const suggestionText = suggestions.length > 0
        ? `\n\n您是否要找:\n${suggestions.map(s => `- ${s.symbol} (${s.name})`).join('\n')}`
        : '';

      alert(`未找到股票: "${term}"${suggestionText}\n\n可搜索的股票包括:\n${Object.values(mockStockData).map(s => `- ${s.symbol} (${s.name})`).join('\n')}`);
      return null;
    }
  };

  // 生成搜索建议
  const generateSearchSuggestions = (term) => {
    const lowerTerm = term.toLowerCase();
    const suggestions = [];

    Object.values(mockStockData).forEach(stock => {
      let relevance = 0;

      // 检查名称相似度
      if (stock.name.toLowerCase().includes(lowerTerm.substring(0, 2))) {
        relevance += 1;
      }

      // 检查别名相似度
      stock.aliases.forEach(alias => {
        if (alias.toLowerCase().includes(lowerTerm.substring(0, 2))) {
          relevance += 1;
        }
      });

      // 检查首字母匹配
      if (stock.name[0].toLowerCase() === lowerTerm[0]) {
        relevance += 0.5;
      }

      if (relevance > 0) {
        suggestions.push({ ...stock, relevance });
      }
    });

    return suggestions
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, 3);
  };

  // 实时搜索建议
  const updateSearchSuggestions = (term) => {
    if (!term || term.length < 1) {
      setSearchSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    const suggestions = generateSearchSuggestions(term);
    setSearchSuggestions(suggestions);
    setShowSuggestions(suggestions.length > 0);
  };

  // 处理搜索输入变化
  const handleSearchInputChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    updateSearchSuggestions(value);
  };

  // 选择搜索建议
  const selectSuggestion = (stock) => {
    setSearchTerm(stock.symbol);
    setShowSuggestions(false);
    setSelectedStock(stock);
    setActiveTab('overview');
  };

  const handleSearch = () => {
    console.log('开始搜索:', searchTerm);
    setShowSuggestions(false);
    if (searchTerm.trim()) {
      searchStock(searchTerm.trim());
    } else {
      alert('请输入股票代码或公司名称');
    }
  };

  // 处理键盘事件
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
    }
  };

  useEffect(() => {
    if (selectedStock) {
      performAnalysis();
    }
  }, [selectedStock]);

  const StockCard = ({ stock }) => (
    <div className="bg-gradient-to-br from-white to-gray-50 rounded-xl p-6 shadow-lg border border-gray-200">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">{stock.name}</h2>
          <p className="text-gray-600">{stock.symbol}</p>
        </div>
        <div className="text-right">
          <div className="text-3xl font-bold text-gray-900">${stock.price}</div>
          <div className={`flex items-center ${stock.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {stock.changePercent >= 0 ? <ArrowUp className="w-4 h-4 mr-1" /> : <ArrowDown className="w-4 h-4 mr-1" />}
            ${Math.abs(stock.change)} ({Math.abs(stock.changePercent)}%)
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div>
          <div className="text-gray-500">市值</div>
          <div className="font-semibold">${(stock.marketCap / 1e9).toFixed(1)}B</div>
        </div>
        <div>
          <div className="text-gray-500">P/E比率</div>
          <div className="font-semibold">{stock.pe}</div>
        </div>
        <div>
          <div className="text-gray-500">成交量</div>
          <div className="font-semibold">{(stock.volume / 1e6).toFixed(1)}M</div>
        </div>
        <div>
          <div className="text-gray-500">Beta</div>
          <div className="font-semibold">{stock.beta}</div>
        </div>
      </div>
    </div>
  );

  const TechnicalAnalysis = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-xl p-6 shadow-lg">
        <h3 className="text-xl font-bold mb-4 flex items-center">
          <Activity className="w-5 h-5 mr-2 text-blue-600" />
          技术指标
        </h3>
        {analysisResults && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="text-sm text-gray-600">SMA20</div>
              <div className="text-lg font-bold">${analysisResults.technicalIndicators.sma20}</div>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <div className="text-sm text-gray-600">SMA50</div>
              <div className="text-lg font-bold">${analysisResults.technicalIndicators.sma50}</div>
            </div>
            <div className="p-4 bg-yellow-50 rounded-lg">
              <div className="text-sm text-gray-600">RSI</div>
              <div className="text-lg font-bold">{analysisResults.technicalIndicators.rsi}</div>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <div className="text-sm text-gray-600">MACD</div>
              <div className="text-lg font-bold">{analysisResults.technicalIndicators.macd}</div>
            </div>
          </div>
        )}
      </div>

      <div className="bg-white rounded-xl p-6 shadow-lg">
        <h3 className="text-xl font-bold mb-4">价格走势图</h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={generateHistoricalData()}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Line type="monotone" dataKey="price" stroke="#3B82F6" strokeWidth={2} />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );

  const PredictionView = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-xl p-6 shadow-lg">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-bold flex items-center">
            <Brain className="w-5 h-5 mr-2 text-purple-600" />
            LSTM预测分析
          </h3>
          <button
            onClick={runLSTMPrediction}
            disabled={isLoading}
            className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:opacity-50 flex items-center"
          >
            {isLoading ? <RefreshCw className="w-4 h-4 mr-2 animate-spin" /> : <Zap className="w-4 h-4 mr-2" />}
            {isLoading ? '分析中...' : '运行预测'}
          </button>
        </div>
        
        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
              <p className="text-gray-600">正在运行LSTM神经网络预测...</p>
            </div>
          </div>
        )}
        
        {predictions && !isLoading && (
          <div>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={predictions}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="predictedPrice" stroke="#8B5CF6" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
            <div className="mt-4 grid grid-cols-2 md:grid-cols-5 gap-4">
              {predictions.slice(0, 5).map((pred, idx) => (
                <div key={idx} className="p-3 bg-purple-50 rounded-lg text-center">
                  <div className="text-sm text-gray-600">第{pred.day}天</div>
                  <div className="font-bold">${pred.predictedPrice}</div>
                  <div className="text-xs text-green-600">置信度: {(pred.confidence * 100).toFixed(1)}%</div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const RecommendationView = () => (
    <div className="space-y-6">
      {analysisResults && (
        <>
          <div className="bg-white rounded-xl p-6 shadow-lg">
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <Target className="w-5 h-5 mr-2 text-green-600" />
              投资建议
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-4">
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  analysisResults.recommendation.action === '买入' ? 'bg-green-100 text-green-800' :
                  analysisResults.recommendation.action === '卖出' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {analysisResults.recommendation.action}
                </div>
                <div className="mt-2 text-sm text-gray-600">建议操作</div>
              </div>
              <div className="text-center p-4">
                <div className="text-xl font-bold text-green-600">${analysisResults.recommendation.buyPrice}</div>
                <div className="text-sm text-gray-600">建议买入价</div>
              </div>
              <div className="text-center p-4">
                <div className="text-xl font-bold text-red-600">${analysisResults.recommendation.sellPrice}</div>
                <div className="text-sm text-gray-600">建议卖出价</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-lg">
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <Calculator className="w-5 h-5 mr-2 text-blue-600" />
              估值分析
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="p-4 border rounded-lg">
                <div className="text-sm text-gray-600">P/E估值</div>
                <div className={`text-lg font-bold ${
                  analysisResults.valuation.peRating === '低估' ? 'text-green-600' :
                  analysisResults.valuation.peRating === '高估' ? 'text-red-600' : 'text-yellow-600'
                }`}>
                  {analysisResults.valuation.peRating}
                </div>
              </div>
              <div className="p-4 border rounded-lg">
                <div className="text-sm text-gray-600">P/B比率</div>
                <div className="text-lg font-bold">{analysisResults.valuation.pbRatio}</div>
              </div>
              <div className="p-4 border rounded-lg">
                <div className="text-sm text-gray-600">股息收益率</div>
                <div className="text-lg font-bold">{analysisResults.valuation.divYield.toFixed(2)}%</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-lg">
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2 text-orange-600" />
              风险评估
            </h3>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-lg font-semibold">{analysisResults.riskLevel}</div>
                <div className="text-sm text-gray-600">基于Beta系数和市场波动性</div>
              </div>
              <div className={`px-4 py-2 rounded-full text-sm font-medium ${
                analysisResults.riskLevel === '低风险' ? 'bg-green-100 text-green-800' :
                analysisResults.riskLevel === '高风险' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
              }`}>
                {analysisResults.riskLevel}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );

  const tabs = [
    { id: 'overview', name: '概览', icon: BarChart3 },
    { id: 'technical', name: '技术分析', icon: Activity },
    { id: 'prediction', name: 'AI预测', icon: Brain },
    { id: 'recommendation', name: '投资建议', icon: Target }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            专业股票分析工具
          </h1>
          <p className="text-gray-600">基于LSTM深度学习的智能股票分析与预测系统</p>
        </div>

        {/* Search */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={searchTerm}
                onChange={handleSearchInputChange}
                onKeyDown={handleKeyPress}
                onFocus={() => updateSearchSuggestions(searchTerm)}
                onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                placeholder="输入股票代码或公司名称 (如: AAPL, 苹果, Tesla, 微软)"
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />

              {/* 搜索建议下拉框 */}
              {showSuggestions && searchSuggestions.length > 0 && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto">
                  {searchSuggestions.map((stock, index) => (
                    <div
                      key={stock.symbol}
                      onClick={() => selectSuggestion(stock)}
                      className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-semibold text-gray-900">{stock.symbol}</div>
                          <div className="text-sm text-gray-600">{stock.name}</div>
                          <div className="text-xs text-gray-500">{stock.sector} • {stock.industry}</div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-gray-900">${stock.price}</div>
                          <div className={`text-sm ${stock.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {stock.changePercent >= 0 ? '+' : ''}{stock.changePercent}%
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            <button
              onClick={handleSearch}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
            >
              <Search className="w-5 h-5 mr-2" />
              搜索
            </button>
          </div>

          <div className="mt-4">
            <div className="flex flex-wrap gap-2 mb-3">
              <span className="text-sm text-gray-600 font-medium">热门股票:</span>
              {Object.values(mockStockData).slice(0, 6).map(stock => (
                <button
                  key={stock.symbol}
                  onClick={() => {
                    setSearchTerm(stock.symbol);
                    setShowSuggestions(false);
                    searchStock(stock.symbol);
                  }}
                  className="bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 px-3 py-1 rounded-full text-sm transition-all duration-200 border border-blue-200 hover:border-blue-300"
                >
                  {stock.symbol} - {stock.name}
                </button>
              ))}
            </div>

            <div className="flex flex-wrap gap-2">
              <span className="text-sm text-gray-600 font-medium">更多选择:</span>
              {Object.values(mockStockData).slice(6).map(stock => (
                <button
                  key={stock.symbol}
                  onClick={() => {
                    setSearchTerm(stock.symbol);
                    setShowSuggestions(false);
                    searchStock(stock.symbol);
                  }}
                  className="bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm transition-colors"
                >
                  {stock.symbol} - {stock.name}
                </button>
              ))}
            </div>

            <div className="mt-3 text-xs text-gray-500">
              💡 提示: 支持中英文搜索，如 "苹果"、"Apple"、"AAPL"、"特斯拉"、"Tesla" 等
            </div>
          </div>
        </div>

        {/* Main Content */}
        {selectedStock && (
          <>
            <StockCard stock={selectedStock} />
            
            {/* Tabs */}
            <div className="mt-8">
              <div className="border-b border-gray-200 bg-white rounded-t-xl">
                <nav className="flex space-x-8 px-6">
                  {tabs.map((tab) => {
                    const Icon = tab.icon;
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                          activeTab === tab.id
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        <Icon className="w-4 h-4 mr-2" />
                        {tab.name}
                      </button>
                    );
                  })}
                </nav>
              </div>
              
              <div className="bg-gray-50 p-6 rounded-b-xl">
                {activeTab === 'overview' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-white rounded-xl p-6 shadow-lg">
                      <h3 className="text-xl font-bold mb-4">基本信息</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-600">行业:</span>
                          <span className="font-semibold">{selectedStock.industry}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">板块:</span>
                          <span className="font-semibold">{selectedStock.sector}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">员工数:</span>
                          <span className="font-semibold">{selectedStock.employees.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">52周最高:</span>
                          <span className="font-semibold">${selectedStock.high52}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">52周最低:</span>
                          <span className="font-semibold">${selectedStock.low52}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white rounded-xl p-6 shadow-lg">
                      <h3 className="text-xl font-bold mb-4">成交量分析</h3>
                      <ResponsiveContainer width="100%" height={200}>
                        <BarChart data={generateHistoricalData().slice(-7)}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="date" />
                          <YAxis />
                          <Tooltip />
                          <Bar dataKey="volume" fill="#3B82F6" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                )}
                
                {activeTab === 'technical' && <TechnicalAnalysis />}
                {activeTab === 'prediction' && <PredictionView />}
                {activeTab === 'recommendation' && <RecommendationView />}
              </div>
            </div>
          </>
        )}
        
        {!selectedStock && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📈</div>
            <h3 className="text-xl font-semibold text-gray-600 mb-2">开始分析股票</h3>
            <p className="text-gray-500 mb-4">在上方搜索框中输入股票代码或公司名称开始分析</p>
            <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-6 max-w-lg mx-auto">
              <h4 className="font-semibold text-yellow-800 mb-3 flex items-center">
                <span className="mr-2">💡</span>
                智能搜索功能
              </h4>
              <div className="text-sm text-yellow-700 space-y-2">
                <div className="flex items-start">
                  <span className="mr-2">🔍</span>
                  <div>
                    <strong>多种搜索方式:</strong> 股票代码(AAPL)、中文名称(苹果)、英文名称(Apple)、关键词(iPhone)
                  </div>
                </div>
                <div className="flex items-start">
                  <span className="mr-2">⚡</span>
                  <div>
                    <strong>实时建议:</strong> 输入时自动显示匹配的股票建议
                  </div>
                </div>
                <div className="flex items-start">
                  <span className="mr-2">🎯</span>
                  <div>
                    <strong>智能匹配:</strong> 支持模糊搜索和容错输入
                  </div>
                </div>
                <div className="flex items-start">
                  <span className="mr-2">⌨️</span>
                  <div>
                    <strong>快捷操作:</strong> 回车搜索，ESC关闭建议，点击快捷按钮
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StockAnalysisTool;