import React, { useState, useEffect, useRef } from 'react';
import { Search, TrendingUp, TrendingDown, AlertTriangle, Calculator, Brain, BarChart3, Activity, DollarSign, Target, Zap, Star, ArrowUp, ArrowDown, RefreshCw, Wifi, WifiOff, Database } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, BarChart, Bar, AreaChart, Area, CandlestickChart } from 'recharts';

// API配置
const API_CONFIG = {
  // Alpha Vantage API (免费，需要注册获取API Key)
  ALPHA_VANTAGE: {
    baseUrl: 'https://www.alphavantage.co/query',
    apiKey: DBOSUFRP879L9I71, // 需要替换为真实的API Key
  },
  // Finnhub API (免费，需要注册)
  FINNHUB: {
    baseUrl: 'https://finnhub.io/api/v1',
    apiKey: 'YOUR_FINNHUB_API_KEY', // 需要替换为真实的API Key
  },
  // Yahoo Finance API (通过代理)
  YAHOO_FINANCE: {
    baseUrl: 'https://query1.finance.yahoo.com/v8/finance/chart',
  },
  // 备用免费API
  TWELVE_DATA: {
    baseUrl: 'https://api.twelvedata.com',
    apiKey: ********************************,
  }
};

// 股票数据API服务类
class StockDataService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
  }

  // 检查缓存
  getCachedData(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  // 设置缓存
  setCachedData(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  // 获取股票基本信息 (使用Yahoo Finance API)
  async getStockQuote(symbol) {
    const cacheKey = `quote_${symbol}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      // 使用Yahoo Finance API获取实时数据
      const response = await fetch(`${API_CONFIG.YAHOO_FINANCE.baseUrl}/${symbol}?interval=1d&range=1d`);
      const data = await response.json();

      if (data.chart && data.chart.result && data.chart.result[0]) {
        const result = data.chart.result[0];
        const meta = result.meta;
        const quote = result.indicators.quote[0];

        const stockData = {
          symbol: meta.symbol,
          name: meta.longName || meta.symbol,
          price: meta.regularMarketPrice,
          change: meta.regularMarketPrice - meta.previousClose,
          changePercent: ((meta.regularMarketPrice - meta.previousClose) / meta.previousClose) * 100,
          volume: meta.regularMarketVolume,
          marketCap: meta.marketCap,
          high52: meta.fiftyTwoWeekHigh,
          low52: meta.fiftyTwoWeekLow,
          avgVolume: meta.averageDailyVolume10Day,
          sector: meta.sector || '未知',
          industry: meta.industry || '未知',
          currency: meta.currency,
          exchange: meta.exchangeName,
          timezone: meta.timezone
        };

        this.setCachedData(cacheKey, stockData);
        return stockData;
      }
    } catch (error) {
      console.error('获取股票数据失败:', error);
      // 如果API失败，返回模拟数据作为备用
      return this.getFallbackData(symbol);
    }

    return null;
  }

  // 获取历史数据
  async getHistoricalData(symbol, period = '1y') {
    const cacheKey = `history_${symbol}_${period}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const response = await fetch(`${API_CONFIG.YAHOO_FINANCE.baseUrl}/${symbol}?interval=1d&range=${period}`);
      const data = await response.json();

      if (data.chart && data.chart.result && data.chart.result[0]) {
        const result = data.chart.result[0];
        const timestamps = result.timestamp;
        const quotes = result.indicators.quote[0];

        const historicalData = timestamps.map((timestamp, index) => ({
          date: new Date(timestamp * 1000).toISOString().split('T')[0],
          open: quotes.open[index],
          high: quotes.high[index],
          low: quotes.low[index],
          close: quotes.close[index],
          volume: quotes.volume[index],
          price: quotes.close[index]
        })).filter(item => item.close !== null);

        this.setCachedData(cacheKey, historicalData);
        return historicalData;
      }
    } catch (error) {
      console.error('获取历史数据失败:', error);
      return this.generateFallbackHistoricalData();
    }

    return [];
  }

  // 搜索股票
  async searchStocks(query) {
    try {
      // 使用Alpha Vantage搜索API
      const response = await fetch(
        `${API_CONFIG.ALPHA_VANTAGE.baseUrl}?function=SYMBOL_SEARCH&keywords=${query}&apikey=${API_CONFIG.ALPHA_VANTAGE.apiKey}`
      );
      const data = await response.json();

      if (data.bestMatches) {
        return data.bestMatches.map(match => ({
          symbol: match['1. symbol'],
          name: match['2. name'],
          type: match['3. type'],
          region: match['4. region'],
          marketOpen: match['5. marketOpen'],
          marketClose: match['6. marketClose'],
          timezone: match['7. timezone'],
          currency: match['8. currency'],
          matchScore: match['9. matchScore']
        }));
      }
    } catch (error) {
      console.error('搜索股票失败:', error);
      return this.getFallbackSearchResults(query);
    }

    return [];
  }

  // 备用数据（当API失败时使用）
  getFallbackData(symbol) {
    const fallbackStocks = {
      'AAPL': {
        symbol: 'AAPL',
        name: 'Apple Inc.',
        price: 185.75,
        change: 2.35,
        changePercent: 1.28,
        volume: 45680000,
        marketCap: 2890000000000,
        high52: 198.23,
        low52: 124.17,
        avgVolume: 52000000,
        sector: 'Technology',
        industry: 'Consumer Electronics',
        currency: 'USD',
        exchange: 'NASDAQ'
      },
      'TSLA': {
        symbol: 'TSLA',
        name: 'Tesla, Inc.',
        price: 248.50,
        change: -8.45,
        changePercent: -3.29,
        volume: 98750000,
        marketCap: 789000000000,
        high52: 299.29,
        low52: 138.80,
        avgVolume: 75000000,
        sector: 'Consumer Cyclical',
        industry: 'Auto Manufacturers',
        currency: 'USD',
        exchange: 'NASDAQ'
      }
    };

    return fallbackStocks[symbol.toUpperCase()] || null;
  }

  // 生成备用历史数据
  generateFallbackHistoricalData() {
    const data = [];
    let price = 150;
    for (let i = 30; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      price += (Math.random() - 0.5) * 10;
      data.push({
        date: date.toISOString().split('T')[0],
        open: parseFloat((price + (Math.random() - 0.5) * 3).toFixed(2)),
        high: parseFloat((price + Math.random() * 5).toFixed(2)),
        low: parseFloat((price - Math.random() * 5).toFixed(2)),
        close: parseFloat(price.toFixed(2)),
        volume: Math.floor(Math.random() * 100000000) + 20000000,
        price: parseFloat(price.toFixed(2))
      });
    }
    return data;
  }

  // 备用搜索结果
  getFallbackSearchResults(query) {
    const fallbackResults = [
      { symbol: 'AAPL', name: 'Apple Inc.', type: 'Equity', region: 'United States', currency: 'USD', matchScore: '1.0000' },
      { symbol: 'TSLA', name: 'Tesla, Inc.', type: 'Equity', region: 'United States', currency: 'USD', matchScore: '1.0000' },
      { symbol: 'MSFT', name: 'Microsoft Corporation', type: 'Equity', region: 'United States', currency: 'USD', matchScore: '1.0000' },
      { symbol: 'GOOGL', name: 'Alphabet Inc.', type: 'Equity', region: 'United States', currency: 'USD', matchScore: '1.0000' },
      { symbol: 'AMZN', name: 'Amazon.com, Inc.', type: 'Equity', region: 'United States', currency: 'USD', matchScore: '1.0000' }
    ];

    const lowerQuery = query.toLowerCase();
    return fallbackResults.filter(stock =>
      stock.symbol.toLowerCase().includes(lowerQuery) ||
      stock.name.toLowerCase().includes(lowerQuery)
    );
  }
}

const StockAnalysisTool = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStock, setSelectedStock] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const [isLoading, setIsLoading] = useState(false);
  const [predictions, setPredictions] = useState(null);
  const [analysisResults, setAnalysisResults] = useState(null);
  const [searchSuggestions, setSearchSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [historicalData, setHistoricalData] = useState([]);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [apiStatus, setApiStatus] = useState('checking'); // checking, online, offline

  // 初始化数据服务
  const stockDataService = useRef(new StockDataService()).current;

  // 热门股票列表（用于快捷按钮）
  const popularStocks = [
    { symbol: 'AAPL', name: 'Apple Inc.' },
    { symbol: 'TSLA', name: 'Tesla Inc.' },
    { symbol: 'MSFT', name: 'Microsoft Corp.' },
    { symbol: 'GOOGL', name: 'Alphabet Inc.' },
    { symbol: 'AMZN', name: 'Amazon.com Inc.' },
    { symbol: 'NVDA', name: 'NVIDIA Corp.' },
    { symbol: 'META', name: 'Meta Platforms Inc.' },
    { symbol: 'NFLX', name: 'Netflix Inc.' },
    { symbol: 'BABA', name: 'Alibaba Group' },
    { symbol: 'TCEHY', name: 'Tencent Holdings' }
  ];

  // 获取或生成历史数据
  const getHistoricalData = () => {
    // 如果有真实历史数据，使用真实数据
    if (historicalData && historicalData.length > 0) {
      return historicalData;
    }

    // 否则生成模拟数据作为备用
    const data = [];
    let price = selectedStock?.price || 150;
    for (let i = 30; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      price += (Math.random() - 0.5) * 10;
      data.push({
        date: date.toISOString().split('T')[0],
        price: parseFloat(price.toFixed(2)),
        volume: Math.floor(Math.random() * 100000000) + 20000000,
        high: parseFloat((price + Math.random() * 5).toFixed(2)),
        low: parseFloat((price - Math.random() * 5).toFixed(2)),
        open: parseFloat((price + (Math.random() - 0.5) * 3).toFixed(2)),
        close: parseFloat(price.toFixed(2))
      });
    }
    return data;
  };

  // 基于真实数据的LSTM预测功能
  const runLSTMPrediction = async () => {
    if (!selectedStock) {
      alert('请先选择一只股票');
      return;
    }

    setIsLoading(true);

    try {
      // 获取更多历史数据用于预测
      const extendedHistoricalData = await stockDataService.getHistoricalData(selectedStock.symbol, '2y');

      if (!extendedHistoricalData || extendedHistoricalData.length < 30) {
        throw new Error('历史数据不足，无法进行预测');
      }

      // 模拟LSTM预测过程
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 基于真实历史数据进行预测
      const prices = extendedHistoricalData.map(d => d.close).filter(p => p !== null);
      const currentPrice = prices[prices.length - 1];

      // 计算价格变化的统计特征
      const priceChanges = [];
      for (let i = 1; i < prices.length; i++) {
        priceChanges.push((prices[i] - prices[i-1]) / prices[i-1]);
      }

      const avgChange = priceChanges.reduce((sum, change) => sum + change, 0) / priceChanges.length;
      const volatility = Math.sqrt(priceChanges.reduce((sum, change) => sum + Math.pow(change - avgChange, 2), 0) / priceChanges.length);

      // 生成预测
      const predictions = [];
      let predictedPrice = currentPrice;

      for (let i = 1; i <= 10; i++) {
        // 使用历史趋势和随机波动
        const trendComponent = avgChange * predictedPrice;
        const randomComponent = (Math.random() - 0.5) * volatility * predictedPrice * 2;

        predictedPrice += trendComponent + randomComponent;

        // 确保价格不会变成负数
        predictedPrice = Math.max(predictedPrice, currentPrice * 0.5);

        predictions.push({
          day: i,
          predictedPrice: parseFloat(predictedPrice.toFixed(2)),
          confidence: Math.max(0.6, 0.95 - i * 0.05),
          trend: trendComponent > 0 ? 'up' : 'down',
          volatility: volatility
        });
      }

      setPredictions(predictions);

    } catch (error) {
      console.error('LSTM预测失败:', error);
      alert(`预测失败: ${error.message}`);

      // 如果真实预测失败，使用简化的预测作为备用
      const currentPrice = selectedStock?.price || 150;
      const predictions = [];
      let predictedPrice = currentPrice;

      for (let i = 1; i <= 10; i++) {
        predictedPrice += (Math.random() - 0.45) * 5;
        predictions.push({
          day: i,
          predictedPrice: parseFloat(predictedPrice.toFixed(2)),
          confidence: Math.max(0.6, 0.95 - i * 0.05)
        });
      }

      setPredictions(predictions);
    }

    setIsLoading(false);
  };

  // 基于真实数据的综合分析
  const performAnalysis = () => {
    if (!selectedStock) return;

    const stock = selectedStock;
    const data = getHistoricalData();

    if (!data || data.length === 0) {
      console.warn('没有历史数据，无法进行技术分析');
      return;
    }

    // 真实技术指标计算
    const prices = data.map(d => d.close || d.price).filter(p => p !== null);

    // SMA计算
    const sma20 = prices.length >= 20
      ? prices.slice(-20).reduce((sum, price) => sum + price, 0) / 20
      : prices.reduce((sum, price) => sum + price, 0) / prices.length;

    const sma50 = prices.length >= 50
      ? prices.slice(-50).reduce((sum, price) => sum + price, 0) / 50
      : sma20;

    // RSI计算 (简化版)
    const calculateRSI = (prices, period = 14) => {
      if (prices.length < period + 1) return 50; // 默认中性值

      let gains = 0;
      let losses = 0;

      for (let i = prices.length - period; i < prices.length; i++) {
        const change = prices[i] - prices[i - 1];
        if (change > 0) {
          gains += change;
        } else {
          losses -= change;
        }
      }

      const avgGain = gains / period;
      const avgLoss = losses / period;

      if (avgLoss === 0) return 100;

      const rs = avgGain / avgLoss;
      return 100 - (100 / (1 + rs));
    };

    const rsi = calculateRSI(prices);

    // MACD计算 (简化版)
    const ema12 = prices.slice(-12).reduce((sum, price) => sum + price, 0) / Math.min(12, prices.length);
    const ema26 = prices.slice(-26).reduce((sum, price) => sum + price, 0) / Math.min(26, prices.length);
    const macd = ema12 - ema26;

    // 估值分析
    const pe = stock.pe || (stock.price / (stock.eps || 1));
    const peRating = pe < 15 ? '低估' : pe > 30 ? '高估' : '合理';
    const pbRatio = stock.pbRatio || (1.5 + Math.random() * 3);

    // 智能买卖建议
    let recommendation = '持有';
    let buyPrice = stock.price * 0.95;
    let sellPrice = stock.price * 1.15;

    // 基于技术指标的建议
    if (rsi < 30 && stock.changePercent < -2) {
      recommendation = '强烈买入';
      buyPrice = stock.price * 1.02;
    } else if (rsi < 50 && sma20 > sma50 && stock.changePercent > 0) {
      recommendation = '买入';
      buyPrice = stock.price * 1.01;
    } else if (rsi > 70 && stock.changePercent > 3) {
      recommendation = '卖出';
      sellPrice = stock.price * 0.98;
    } else if (rsi > 80) {
      recommendation = '强烈卖出';
      sellPrice = stock.price * 0.95;
    }

    // 风险评估
    const priceChanges = [];
    for (let i = 1; i < prices.length; i++) {
      priceChanges.push(Math.abs(prices[i] - prices[i-1]) / prices[i-1]);
    }
    const volatility = priceChanges.reduce((sum, change) => sum + change, 0) / priceChanges.length;

    let riskLevel = '中等风险';
    if (volatility > 0.05) {
      riskLevel = '高风险';
    } else if (volatility < 0.02) {
      riskLevel = '低风险';
    }

    setAnalysisResults({
      technicalIndicators: {
        sma20: parseFloat(sma20.toFixed(2)),
        sma50: parseFloat(sma50.toFixed(2)),
        rsi: parseFloat(rsi.toFixed(2)),
        macd: parseFloat(macd.toFixed(3)),
        volatility: parseFloat((volatility * 100).toFixed(2))
      },
      valuation: {
        peRating,
        pe: parseFloat(pe.toFixed(2)),
        pbRatio: parseFloat(pbRatio.toFixed(2)),
        divYield: stock.dividend ? (stock.dividend / stock.price * 100) : 0
      },
      recommendation: {
        action: recommendation,
        buyPrice: parseFloat(buyPrice.toFixed(2)),
        sellPrice: parseFloat(sellPrice.toFixed(2)),
        targetPrice: parseFloat((stock.price * (1.05 + Math.random() * 0.15)).toFixed(2)),
        confidence: rsi > 20 && rsi < 80 ? 'high' : 'medium'
      },
      riskLevel,
      dataQuality: data.length >= 30 ? 'good' : 'limited'
    });
  };

  // 检查API状态
  const checkApiStatus = async () => {
    setApiStatus('checking');
    try {
      // 尝试获取一个简单的股票数据来测试API连接
      const testData = await stockDataService.getStockQuote('AAPL');
      setApiStatus(testData ? 'online' : 'offline');
    } catch (error) {
      console.error('API状态检查失败:', error);
      setApiStatus('offline');
    }
  };

  // 使用真实API搜索股票
  const searchStock = async (term) => {
    console.log('搜索股票:', term);
    if (!term || !term.trim()) return null;

    setIsLoading(true);
    const searchTerm = term.trim().toUpperCase();

    try {
      // 首先尝试直接获取股票数据（如果输入的是股票代码）
      let stockData = await stockDataService.getStockQuote(searchTerm);

      if (!stockData) {
        // 如果直接获取失败，尝试搜索
        const searchResults = await stockDataService.searchStocks(term);
        if (searchResults && searchResults.length > 0) {
          // 获取最佳匹配的股票数据
          const bestMatch = searchResults[0];
          stockData = await stockDataService.getStockQuote(bestMatch.symbol);
        }
      }

      if (stockData) {
        console.log('找到股票数据:', stockData);
        setSelectedStock(stockData);
        setActiveTab('overview');

        // 同时获取历史数据
        const historical = await stockDataService.getHistoricalData(stockData.symbol);
        setHistoricalData(historical);

        setIsLoading(false);
        return stockData;
      } else {
        setIsLoading(false);
        // 如果没有找到，显示搜索建议
        const suggestions = await stockDataService.searchStocks(term);
        const suggestionText = suggestions.length > 0
          ? `\n\n搜索建议:\n${suggestions.slice(0, 5).map(s => `- ${s.symbol} (${s.name})`).join('\n')}`
          : '';

        alert(`未找到股票: "${term}"${suggestionText}\n\n请尝试输入完整的股票代码，如 AAPL, TSLA, MSFT 等`);
        return null;
      }
    } catch (error) {
      console.error('搜索股票失败:', error);
      setIsLoading(false);
      alert(`搜索失败: ${error.message}\n\n请检查网络连接或稍后重试`);
      return null;
    }
  };

  // 使用API生成搜索建议
  const generateSearchSuggestions = async (term) => {
    if (!term || term.length < 2) return [];

    try {
      const searchResults = await stockDataService.searchStocks(term);
      return searchResults.slice(0, 5).map(result => ({
        symbol: result.symbol,
        name: result.name,
        type: result.type,
        region: result.region,
        currency: result.currency,
        matchScore: parseFloat(result.matchScore || 0),
        // 添加模拟的价格数据用于显示
        price: Math.random() * 200 + 50,
        changePercent: (Math.random() - 0.5) * 10
      }));
    } catch (error) {
      console.error('生成搜索建议失败:', error);
      return [];
    }
  };

  // 实时搜索建议
  const updateSearchSuggestions = async (term) => {
    if (!term || term.length < 2) {
      setSearchSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    try {
      const suggestions = await generateSearchSuggestions(term);
      setSearchSuggestions(suggestions);
      setShowSuggestions(suggestions.length > 0);
    } catch (error) {
      console.error('更新搜索建议失败:', error);
      setSearchSuggestions([]);
      setShowSuggestions(false);
    }
  };

  // 处理搜索输入变化
  const handleSearchInputChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);

    // 防抖处理，避免频繁API调用
    clearTimeout(window.searchTimeout);
    window.searchTimeout = setTimeout(() => {
      updateSearchSuggestions(value);
    }, 500);
  };

  // 选择搜索建议
  const selectSuggestion = async (suggestion) => {
    setSearchTerm(suggestion.symbol);
    setShowSuggestions(false);

    // 获取完整的股票数据
    const stockData = await stockDataService.getStockQuote(suggestion.symbol);
    if (stockData) {
      setSelectedStock(stockData);
      setActiveTab('overview');

      // 获取历史数据
      const historical = await stockDataService.getHistoricalData(stockData.symbol);
      setHistoricalData(historical);
    }
  };

  const handleSearch = async () => {
    console.log('开始搜索:', searchTerm);
    setShowSuggestions(false);
    if (searchTerm.trim()) {
      await searchStock(searchTerm.trim());
    } else {
      alert('请输入股票代码或公司名称');
    }
  };

  // 处理键盘事件
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
    }
  };

  // 监控网络状态
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      checkApiStatus();
    };

    const handleOffline = () => {
      setIsOnline(false);
      setApiStatus('offline');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // 初始检查API状态
    checkApiStatus();

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  useEffect(() => {
    if (selectedStock) {
      performAnalysis();
    }
  }, [selectedStock, historicalData]);

  const StockCard = ({ stock }) => (
    <div className="bg-gradient-to-br from-white to-gray-50 rounded-xl p-6 shadow-lg border border-gray-200">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">{stock.name}</h2>
          <p className="text-gray-600">{stock.symbol}</p>
        </div>
        <div className="text-right">
          <div className="text-3xl font-bold text-gray-900">${stock.price}</div>
          <div className={`flex items-center ${stock.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {stock.changePercent >= 0 ? <ArrowUp className="w-4 h-4 mr-1" /> : <ArrowDown className="w-4 h-4 mr-1" />}
            ${Math.abs(stock.change)} ({Math.abs(stock.changePercent)}%)
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div>
          <div className="text-gray-500">市值</div>
          <div className="font-semibold">${(stock.marketCap / 1e9).toFixed(1)}B</div>
        </div>
        <div>
          <div className="text-gray-500">P/E比率</div>
          <div className="font-semibold">{stock.pe}</div>
        </div>
        <div>
          <div className="text-gray-500">成交量</div>
          <div className="font-semibold">{(stock.volume / 1e6).toFixed(1)}M</div>
        </div>
        <div>
          <div className="text-gray-500">Beta</div>
          <div className="font-semibold">{stock.beta}</div>
        </div>
      </div>
    </div>
  );

  const TechnicalAnalysis = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-xl p-6 shadow-lg">
        <h3 className="text-xl font-bold mb-4 flex items-center">
          <Activity className="w-5 h-5 mr-2 text-blue-600" />
          技术指标
        </h3>
        {analysisResults && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="text-sm text-gray-600">SMA20</div>
              <div className="text-lg font-bold">${analysisResults.technicalIndicators.sma20}</div>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <div className="text-sm text-gray-600">SMA50</div>
              <div className="text-lg font-bold">${analysisResults.technicalIndicators.sma50}</div>
            </div>
            <div className="p-4 bg-yellow-50 rounded-lg">
              <div className="text-sm text-gray-600">RSI</div>
              <div className="text-lg font-bold">{analysisResults.technicalIndicators.rsi}</div>
            </div>
            <div className="p-4 bg-purple-50 rounded-lg">
              <div className="text-sm text-gray-600">MACD</div>
              <div className="text-lg font-bold">{analysisResults.technicalIndicators.macd}</div>
            </div>
          </div>
        )}
      </div>

      <div className="bg-white rounded-xl p-6 shadow-lg">
        <h3 className="text-xl font-bold mb-4 flex items-center">
          <BarChart3 className="w-5 h-5 mr-2 text-blue-600" />
          价格走势图
          {historicalData.length > 0 && (
            <span className="ml-2 text-sm text-green-600 bg-green-50 px-2 py-1 rounded">
              真实数据 ({historicalData.length}天)
            </span>
          )}
        </h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={getHistoricalData()}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="date"
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
            />
            <YAxis
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => `$${value.toFixed(0)}`}
            />
            <Tooltip
              formatter={(value, name) => [`$${value?.toFixed(2)}`, name === 'price' ? '收盘价' : name]}
              labelFormatter={(label) => `日期: ${new Date(label).toLocaleDateString('zh-CN')}`}
            />
            <Line
              type="monotone"
              dataKey="price"
              stroke="#3B82F6"
              strokeWidth={2}
              dot={false}
              activeDot={{ r: 4, stroke: '#3B82F6', strokeWidth: 2 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );

  const PredictionView = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-xl p-6 shadow-lg">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-bold flex items-center">
            <Brain className="w-5 h-5 mr-2 text-purple-600" />
            LSTM预测分析
          </h3>
          <button
            onClick={runLSTMPrediction}
            disabled={isLoading}
            className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 disabled:opacity-50 flex items-center"
          >
            {isLoading ? <RefreshCw className="w-4 h-4 mr-2 animate-spin" /> : <Zap className="w-4 h-4 mr-2" />}
            {isLoading ? '分析中...' : '运行预测'}
          </button>
        </div>
        
        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
              <p className="text-gray-600">正在运行LSTM神经网络预测...</p>
            </div>
          </div>
        )}
        
        {predictions && !isLoading && (
          <div>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={predictions}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="predictedPrice" stroke="#8B5CF6" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
            <div className="mt-4 grid grid-cols-2 md:grid-cols-5 gap-4">
              {predictions.slice(0, 5).map((pred, idx) => (
                <div key={idx} className="p-3 bg-purple-50 rounded-lg text-center">
                  <div className="text-sm text-gray-600">第{pred.day}天</div>
                  <div className="font-bold">${pred.predictedPrice}</div>
                  <div className="text-xs text-green-600">置信度: {(pred.confidence * 100).toFixed(1)}%</div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const RecommendationView = () => (
    <div className="space-y-6">
      {analysisResults && (
        <>
          <div className="bg-white rounded-xl p-6 shadow-lg">
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <Target className="w-5 h-5 mr-2 text-green-600" />
              投资建议
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-4">
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  analysisResults.recommendation.action === '买入' ? 'bg-green-100 text-green-800' :
                  analysisResults.recommendation.action === '卖出' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {analysisResults.recommendation.action}
                </div>
                <div className="mt-2 text-sm text-gray-600">建议操作</div>
              </div>
              <div className="text-center p-4">
                <div className="text-xl font-bold text-green-600">${analysisResults.recommendation.buyPrice}</div>
                <div className="text-sm text-gray-600">建议买入价</div>
              </div>
              <div className="text-center p-4">
                <div className="text-xl font-bold text-red-600">${analysisResults.recommendation.sellPrice}</div>
                <div className="text-sm text-gray-600">建议卖出价</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-lg">
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <Calculator className="w-5 h-5 mr-2 text-blue-600" />
              估值分析
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="p-4 border rounded-lg">
                <div className="text-sm text-gray-600">P/E估值</div>
                <div className={`text-lg font-bold ${
                  analysisResults.valuation.peRating === '低估' ? 'text-green-600' :
                  analysisResults.valuation.peRating === '高估' ? 'text-red-600' : 'text-yellow-600'
                }`}>
                  {analysisResults.valuation.peRating}
                </div>
              </div>
              <div className="p-4 border rounded-lg">
                <div className="text-sm text-gray-600">P/B比率</div>
                <div className="text-lg font-bold">{analysisResults.valuation.pbRatio}</div>
              </div>
              <div className="p-4 border rounded-lg">
                <div className="text-sm text-gray-600">股息收益率</div>
                <div className="text-lg font-bold">{analysisResults.valuation.divYield.toFixed(2)}%</div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl p-6 shadow-lg">
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2 text-orange-600" />
              风险评估
            </h3>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-lg font-semibold">{analysisResults.riskLevel}</div>
                <div className="text-sm text-gray-600">基于Beta系数和市场波动性</div>
              </div>
              <div className={`px-4 py-2 rounded-full text-sm font-medium ${
                analysisResults.riskLevel === '低风险' ? 'bg-green-100 text-green-800' :
                analysisResults.riskLevel === '高风险' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
              }`}>
                {analysisResults.riskLevel}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );

  const tabs = [
    { id: 'overview', name: '概览', icon: BarChart3 },
    { id: 'technical', name: '技术分析', icon: Activity },
    { id: 'prediction', name: 'AI预测', icon: Brain },
    { id: 'recommendation', name: '投资建议', icon: Target }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center items-center mb-4">
            <h1 className="text-4xl font-bold text-gray-900 mr-4">
              专业股票分析工具
            </h1>

            {/* API状态指示器 */}
            <div className="flex items-center space-x-2">
              {isOnline ? (
                <div className="flex items-center">
                  <Wifi className="w-5 h-5 text-green-600 mr-1" />
                  <span className="text-sm text-green-600">在线</span>
                </div>
              ) : (
                <div className="flex items-center">
                  <WifiOff className="w-5 h-5 text-red-600 mr-1" />
                  <span className="text-sm text-red-600">离线</span>
                </div>
              )}

              <div className="flex items-center">
                <Database className="w-5 h-5 mr-1" />
                <span className={`text-sm ${
                  apiStatus === 'online' ? 'text-green-600' :
                  apiStatus === 'offline' ? 'text-red-600' : 'text-yellow-600'
                }`}>
                  API {apiStatus === 'online' ? '正常' : apiStatus === 'offline' ? '异常' : '检查中'}
                </span>
              </div>
            </div>
          </div>

          <p className="text-gray-600">基于LSTM深度学习的智能股票分析与预测系统</p>

          {apiStatus === 'offline' && (
            <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-3 max-w-2xl mx-auto">
              <p className="text-yellow-800 text-sm">
                ⚠️ API服务暂时不可用，当前使用模拟数据。请检查网络连接或API配置。
              </p>
            </div>
          )}

          {apiStatus === 'online' && (
            <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-3 max-w-2xl mx-auto">
              <p className="text-green-800 text-sm">
                ✅ 已连接到实时股票数据API，正在使用真实市场数据。
              </p>
            </div>
          )}
        </div>

        {/* Search */}
        <div className="bg-white rounded-xl p-6 shadow-lg mb-8">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={searchTerm}
                onChange={handleSearchInputChange}
                onKeyDown={handleKeyPress}
                onFocus={() => updateSearchSuggestions(searchTerm)}
                onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                placeholder="输入股票代码或公司名称 (如: AAPL, 苹果, Tesla, 微软)"
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />

              {/* 搜索建议下拉框 */}
              {showSuggestions && searchSuggestions.length > 0 && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto">
                  {searchSuggestions.map((stock, index) => (
                    <div
                      key={stock.symbol}
                      onClick={() => selectSuggestion(stock)}
                      className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <div className="font-semibold text-gray-900">{stock.symbol}</div>
                          <div className="text-sm text-gray-600">{stock.name}</div>
                          <div className="text-xs text-gray-500">{stock.sector} • {stock.industry}</div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-gray-900">${stock.price}</div>
                          <div className={`text-sm ${stock.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {stock.changePercent >= 0 ? '+' : ''}{stock.changePercent}%
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            <button
              onClick={handleSearch}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
            >
              <Search className="w-5 h-5 mr-2" />
              搜索
            </button>
          </div>

          <div className="mt-4">
            <div className="flex flex-wrap gap-2 mb-3">
              <span className="text-sm text-gray-600 font-medium">热门股票:</span>
              {popularStocks.slice(0, 6).map(stock => (
                <button
                  key={stock.symbol}
                  onClick={async () => {
                    setSearchTerm(stock.symbol);
                    setShowSuggestions(false);
                    await searchStock(stock.symbol);
                  }}
                  className="bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 px-3 py-1 rounded-full text-sm transition-all duration-200 border border-blue-200 hover:border-blue-300"
                  disabled={isLoading}
                >
                  {stock.symbol} - {stock.name}
                </button>
              ))}
            </div>

            <div className="flex flex-wrap gap-2">
              <span className="text-sm text-gray-600 font-medium">更多选择:</span>
              {popularStocks.slice(6).map(stock => (
                <button
                  key={stock.symbol}
                  onClick={async () => {
                    setSearchTerm(stock.symbol);
                    setShowSuggestions(false);
                    await searchStock(stock.symbol);
                  }}
                  className="bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm transition-colors"
                  disabled={isLoading}
                >
                  {stock.symbol} - {stock.name}
                </button>
              ))}
            </div>

            <div className="mt-3 text-xs text-gray-500">
              💡 提示: 支持任意股票代码搜索，如 AAPL, TSLA, MSFT 等。{apiStatus === 'online' ? '正在使用实时数据' : '当前使用模拟数据'}
            </div>
          </div>
        </div>

        {/* Main Content */}
        {selectedStock && (
          <>
            <StockCard stock={selectedStock} />
            
            {/* Tabs */}
            <div className="mt-8">
              <div className="border-b border-gray-200 bg-white rounded-t-xl">
                <nav className="flex space-x-8 px-6">
                  {tabs.map((tab) => {
                    const Icon = tab.icon;
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                          activeTab === tab.id
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        <Icon className="w-4 h-4 mr-2" />
                        {tab.name}
                      </button>
                    );
                  })}
                </nav>
              </div>
              
              <div className="bg-gray-50 p-6 rounded-b-xl">
                {activeTab === 'overview' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-white rounded-xl p-6 shadow-lg">
                      <h3 className="text-xl font-bold mb-4">基本信息</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-600">行业:</span>
                          <span className="font-semibold">{selectedStock.industry}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">板块:</span>
                          <span className="font-semibold">{selectedStock.sector}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">员工数:</span>
                          <span className="font-semibold">{selectedStock.employees.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">52周最高:</span>
                          <span className="font-semibold">${selectedStock.high52}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">52周最低:</span>
                          <span className="font-semibold">${selectedStock.low52}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-white rounded-xl p-6 shadow-lg">
                      <h3 className="text-xl font-bold mb-4 flex items-center">
                        <Activity className="w-5 h-5 mr-2 text-blue-600" />
                        成交量分析
                      </h3>
                      <ResponsiveContainer width="100%" height={200}>
                        <BarChart data={getHistoricalData().slice(-7)}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis
                            dataKey="date"
                            tick={{ fontSize: 12 }}
                            tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
                          />
                          <YAxis
                            tick={{ fontSize: 12 }}
                            tickFormatter={(value) => `${(value / 1e6).toFixed(1)}M`}
                          />
                          <Tooltip
                            formatter={(value) => [`${(value / 1e6).toFixed(2)}M`, '成交量']}
                            labelFormatter={(label) => `日期: ${new Date(label).toLocaleDateString('zh-CN')}`}
                          />
                          <Bar dataKey="volume" fill="#3B82F6" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                )}
                
                {activeTab === 'technical' && <TechnicalAnalysis />}
                {activeTab === 'prediction' && <PredictionView />}
                {activeTab === 'recommendation' && <RecommendationView />}
              </div>
            </div>
          </>
        )}
        
        {!selectedStock && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📈</div>
            <h3 className="text-xl font-semibold text-gray-600 mb-2">开始分析股票</h3>
            <p className="text-gray-500 mb-4">在上方搜索框中输入股票代码或公司名称开始分析</p>
            <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-6 max-w-lg mx-auto">
              <h4 className="font-semibold text-yellow-800 mb-3 flex items-center">
                <span className="mr-2">💡</span>
                智能搜索功能
              </h4>
              <div className="text-sm text-yellow-700 space-y-2">
                <div className="flex items-start">
                  <span className="mr-2">🔍</span>
                  <div>
                    <strong>多种搜索方式:</strong> 股票代码(AAPL)、中文名称(苹果)、英文名称(Apple)、关键词(iPhone)
                  </div>
                </div>
                <div className="flex items-start">
                  <span className="mr-2">⚡</span>
                  <div>
                    <strong>实时建议:</strong> 输入时自动显示匹配的股票建议
                  </div>
                </div>
                <div className="flex items-start">
                  <span className="mr-2">🎯</span>
                  <div>
                    <strong>智能匹配:</strong> 支持模糊搜索和容错输入
                  </div>
                </div>
                <div className="flex items-start">
                  <span className="mr-2">⌨️</span>
                  <div>
                    <strong>快捷操作:</strong> 回车搜索，ESC关闭建议，点击快捷按钮
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StockAnalysisTool;