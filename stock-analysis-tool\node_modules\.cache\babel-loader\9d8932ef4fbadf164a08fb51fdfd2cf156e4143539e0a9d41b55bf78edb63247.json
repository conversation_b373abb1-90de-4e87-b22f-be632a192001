{"ast": null, "code": "import exponent from \"./exponent.js\";\nexport default function (step, value) {\n  return Math.max(0, Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3 - exponent(Math.abs(step)));\n}", "map": {"version": 3, "names": ["exponent", "step", "value", "Math", "max", "min", "floor", "abs"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/d3-format/src/precisionPrefix.js"], "sourcesContent": ["import exponent from \"./exponent.js\";\n\nexport default function(step, value) {\n  return Math.max(0, Math.max(-8, Math.min(8, Math.floor(exponent(value) / 3))) * 3 - exponent(Math.abs(step)));\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AAEpC,eAAe,UAASC,IAAI,EAAEC,KAAK,EAAE;EACnC,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACG,KAAK,CAACN,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGF,QAAQ,CAACG,IAAI,CAACI,GAAG,CAACN,IAAI,CAAC,CAAC,CAAC;AAC/G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}