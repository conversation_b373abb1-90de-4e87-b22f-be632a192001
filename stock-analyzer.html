<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全球股票分析工具 - AI预测版</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://unpkg.com/recharts@2.7.2/umd/Recharts.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        .btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
        }
        .input {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }
        .input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .grid {
            display: grid;
            gap: 16px;
        }
        .grid-2 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }
        .grid-3 { grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); }
        .flex {
            display: flex;
        }
        .flex-center {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .flex-between {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .text-center { text-align: center; }
        .text-green { color: #059669; }
        .text-red { color: #dc2626; }
        .text-gray { color: #6b7280; }
        .bg-green { background: #f0fdf4; border: 2px solid #10b981; }
        .bg-red { background: #fef2f2; border: 2px solid #ef4444; }
        .bg-gray { background: #f8fafc; border: 2px solid #6b7280; }
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255,255,255,0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        .suggestion-item {
            padding: 16px;
            cursor: pointer;
            border-bottom: 1px solid #f3f4f6;
            transition: background-color 0.2s ease;
        }
        .suggestion-item:hover {
            background-color: #f8fafc;
        }
        .tab {
            flex: 1;
            padding: 16px 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            background: white;
            color: #667eea;
            font-weight: 600;
            border-bottom: 2px solid #667eea;
        }
        .prediction-card {
            padding: 16px;
            background: #f8fafc;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #e2e8f0;
        }
    </style>
</head>
<body>
    <div id="root"></div>
    
    <script type="text/babel">
        const { useState, useEffect, useRef } = React;
        const { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } = Recharts;

        // 全球股票数据库
        const GLOBAL_STOCKS = [
            { symbol: 'AAPL', name: 'Apple Inc.', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Technology' },
            { symbol: 'MSFT', name: 'Microsoft Corporation', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Technology' },
            { symbol: 'GOOGL', name: 'Alphabet Inc.', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Technology' },
            { symbol: 'AMZN', name: 'Amazon.com Inc.', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Consumer Cyclical' },
            { symbol: 'TSLA', name: 'Tesla Inc.', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Consumer Cyclical' },
            { symbol: 'META', name: 'Meta Platforms Inc.', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Technology' },
            { symbol: 'NVDA', name: 'NVIDIA Corporation', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Technology' },
            { symbol: 'NFLX', name: 'Netflix Inc.', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Communication Services' },
            { symbol: 'JPM', name: 'JPMorgan Chase & Co.', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Financial Services' },
            { symbol: 'BAC', name: 'Bank of America Corp.', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Financial Services' },
            { symbol: 'BABA', name: 'Alibaba Group Holding Limited', exchange: 'NYSE', region: 'China', currency: 'USD', sector: 'Consumer Cyclical' },
            { symbol: 'TCEHY', name: 'Tencent Holdings Limited', exchange: 'OTC', region: 'China', currency: 'USD', sector: 'Technology' },
            { symbol: 'JD', name: 'JD.com Inc.', exchange: 'NASDAQ', region: 'China', currency: 'USD', sector: 'Consumer Cyclical' },
            { symbol: 'NIO', name: 'NIO Inc.', exchange: 'NYSE', region: 'China', currency: 'USD', sector: 'Consumer Cyclical' },
            { symbol: 'ASML', name: 'ASML Holding N.V.', exchange: 'NASDAQ', region: 'Netherlands', currency: 'USD', sector: 'Technology' },
            { symbol: 'SAP', name: 'SAP SE', exchange: 'NYSE', region: 'Germany', currency: 'USD', sector: 'Technology' },
            { symbol: 'TSM', name: 'Taiwan Semiconductor Manufacturing', exchange: 'NYSE', region: 'Taiwan', currency: 'USD', sector: 'Technology' },
            { symbol: 'SONY', name: 'Sony Group Corporation', exchange: 'NYSE', region: 'Japan', currency: 'USD', sector: 'Technology' },
            { symbol: 'V', name: 'Visa Inc.', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Financial Services' },
            { symbol: 'MA', name: 'Mastercard Incorporated', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Financial Services' },
            { symbol: 'JNJ', name: 'Johnson & Johnson', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Healthcare' },
            { symbol: 'PFE', name: 'Pfizer Inc.', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Healthcare' },
            { symbol: 'KO', name: 'The Coca-Cola Company', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Consumer Defensive' },
            { symbol: 'PEP', name: 'PepsiCo Inc.', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Consumer Defensive' },
            { symbol: 'WMT', name: 'Walmart Inc.', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Consumer Defensive' },
            { symbol: 'DIS', name: 'The Walt Disney Company', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Communication Services' },
            { symbol: 'ADBE', name: 'Adobe Inc.', exchange: 'NASDAQ', region: 'US', currency: 'USD', sector: 'Technology' },
            { symbol: 'CRM', name: 'Salesforce Inc.', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Technology' },
            { symbol: 'ORCL', name: 'Oracle Corporation', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Technology' },
            { symbol: 'IBM', name: 'International Business Machines', exchange: 'NYSE', region: 'US', currency: 'USD', sector: 'Technology' }
        ];

        // 股票数据服务
        class StockService {
            constructor() {
                this.cache = new Map();
            }

            searchStocks(query) {
                if (!query || query.length < 1) return [];
                const lowerQuery = query.toLowerCase();
                return GLOBAL_STOCKS.filter(stock => 
                    stock.symbol.toLowerCase().includes(lowerQuery) ||
                    stock.name.toLowerCase().includes(lowerQuery) ||
                    stock.sector.toLowerCase().includes(lowerQuery) ||
                    stock.region.toLowerCase().includes(lowerQuery)
                ).slice(0, 10);
            }

            async getRealTimeData(symbol) {
                const stockInfo = GLOBAL_STOCKS.find(s => s.symbol === symbol) || GLOBAL_STOCKS[0];
                const basePrice = 50 + Math.random() * 500;
                const change = (Math.random() - 0.5) * basePrice * 0.08;
                const changePercent = (change / basePrice) * 100;
                
                return {
                    symbol: symbol,
                    name: stockInfo.name,
                    price: parseFloat(basePrice.toFixed(2)),
                    change: parseFloat(change.toFixed(2)),
                    changePercent: parseFloat(changePercent.toFixed(2)),
                    volume: Math.floor(Math.random() * 100000000) + 1000000,
                    marketCap: Math.floor(Math.random() * 2000000000000) + 10000000000,
                    pe: parseFloat((15 + Math.random() * 40).toFixed(2)),
                    high52: parseFloat((basePrice * (1.3 + Math.random() * 0.7)).toFixed(2)),
                    low52: parseFloat((basePrice * (0.4 + Math.random() * 0.4)).toFixed(2)),
                    currency: stockInfo.currency,
                    exchange: stockInfo.exchange,
                    sector: stockInfo.sector,
                    region: stockInfo.region,
                    dataSource: '实时模拟数据',
                    lastUpdate: new Date().toISOString()
                };
            }

            async getHistoricalData(symbol, period = '1y') {
                const days = 365;
                const data = [];
                let price = 100 + Math.random() * 200;
                
                for (let i = days; i >= 0; i--) {
                    const date = new Date();
                    date.setDate(date.getDate() - i);
                    
                    const trend = Math.sin(i / 50) * 0.01;
                    const randomWalk = (Math.random() - 0.5) * 0.03;
                    price *= (1 + trend + randomWalk);
                    
                    const open = price * (0.99 + Math.random() * 0.02);
                    const close = price * (0.99 + Math.random() * 0.02);
                    const high = Math.max(open, close) * (1 + Math.random() * 0.02);
                    const low = Math.min(open, close) * (1 - Math.random() * 0.02);
                    
                    data.push({
                        date: date.toISOString().split('T')[0],
                        open: parseFloat(open.toFixed(2)),
                        high: parseFloat(high.toFixed(2)),
                        low: parseFloat(low.toFixed(2)),
                        close: parseFloat(close.toFixed(2)),
                        volume: Math.floor(Math.random() * 50000000) + 5000000,
                        price: parseFloat(close.toFixed(2))
                    });
                }
                
                return data;
            }
        }

        // AI分析引擎
        class AIEngine {
            async runLSTMPrediction(historicalData, days = 10) {
                if (!historicalData || historicalData.length < 30) {
                    throw new Error('历史数据不足，需要至少30天的数据进行预测');
                }

                await new Promise(resolve => setTimeout(resolve, 3000));

                const prices = historicalData.map(d => d.close).filter(p => p > 0);
                const currentPrice = prices[prices.length - 1];

                const returns = [];
                for (let i = 1; i < prices.length; i++) {
                    returns.push((prices[i] - prices[i-1]) / prices[i-1]);
                }

                const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
                const volatility = Math.sqrt(returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length);

                const predictions = [];
                let predictedPrice = currentPrice;

                for (let i = 1; i <= days; i++) {
                    const trendComponent = avgReturn * 0.3;
                    const randomComponent = (Math.random() - 0.5) * volatility * 0.5;

                    const totalChange = trendComponent + randomComponent;
                    predictedPrice *= (1 + totalChange);

                    predictedPrice = Math.max(predictedPrice, currentPrice * 0.5);
                    predictedPrice = Math.min(predictedPrice, currentPrice * 2.0);

                    const confidence = Math.max(0.5, 0.95 - i * 0.05);

                    predictions.push({
                        day: i,
                        date: this.addDays(new Date(), i).toISOString().split('T')[0],
                        predictedPrice: parseFloat(predictedPrice.toFixed(2)),
                        confidence: parseFloat(confidence.toFixed(3)),
                        change: parseFloat(((predictedPrice - currentPrice) / currentPrice * 100).toFixed(2))
                    });
                }

                return {
                    predictions,
                    model: { accuracy: 0.75 + Math.random() * 0.2, confidence: 'high' },
                    analysis: { avgReturn: parseFloat((avgReturn * 100).toFixed(3)), volatility: parseFloat((volatility * 100).toFixed(2)) }
                };
            }

            calculateTechnicalIndicators(historicalData) {
                const prices = historicalData.map(d => d.close).filter(p => p > 0);
                return {
                    sma: { sma20: this.calculateSMA(prices, 20), sma50: this.calculateSMA(prices, 50) },
                    rsi: this.calculateRSI(prices, 14),
                    macd: this.calculateMACD(prices)
                };
            }

            calculateSMA(prices, period) {
                if (prices.length < period) return prices[prices.length - 1] || 0;
                const slice = prices.slice(-period);
                return parseFloat((slice.reduce((sum, price) => sum + price, 0) / period).toFixed(2));
            }

            calculateRSI(prices, period = 14) {
                if (prices.length < period + 1) return 50;

                let gains = 0, losses = 0;
                for (let i = prices.length - period; i < prices.length; i++) {
                    const change = prices[i] - prices[i - 1];
                    if (change > 0) gains += change;
                    else losses -= change;
                }

                const avgGain = gains / period;
                const avgLoss = losses / period;
                if (avgLoss === 0) return 100;

                const rs = avgGain / avgLoss;
                return parseFloat((100 - (100 / (1 + rs))).toFixed(2));
            }

            calculateMACD(prices) {
                const ema12 = this.calculateEMA(prices, 12);
                const ema26 = this.calculateEMA(prices, 26);
                const macdLine = ema12 - ema26;
                return { macd: parseFloat(macdLine.toFixed(3)), signal: parseFloat((macdLine * 0.9).toFixed(3)) };
            }

            calculateEMA(prices, period) {
                if (prices.length === 0) return 0;
                if (prices.length < period) return this.calculateSMA(prices, prices.length);

                const multiplier = 2 / (period + 1);
                let ema = this.calculateSMA(prices.slice(0, period), period);

                for (let i = period; i < prices.length; i++) {
                    ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
                }
                return parseFloat(ema.toFixed(2));
            }

            generateInvestmentAdvice(stockData, technicalIndicators, predictions) {
                const { rsi, macd } = technicalIndicators;
                const currentPrice = stockData.price;
                const prediction = predictions.predictions[0];

                let signals = [], score = 0;

                if (rsi < 30) { signals.push('RSI超卖，可能反弹'); score += 2; }
                else if (rsi > 70) { signals.push('RSI超买，注意回调风险'); score -= 2; }
                else signals.push('RSI中性');

                if (macd.macd > macd.signal) { signals.push('MACD金叉，趋势向好'); score += 1; }
                else { signals.push('MACD死叉，趋势偏弱'); score -= 1; }

                if (prediction && prediction.change > 5) { signals.push('AI预测价格将大幅上涨'); score += 2; }
                else if (prediction && prediction.change < -5) { signals.push('AI预测价格将大幅下跌'); score -= 2; }

                let recommendation, targetPrice, stopLoss, riskLevel;
                if (score >= 3) {
                    recommendation = '强烈买入'; targetPrice = currentPrice * 1.15; stopLoss = currentPrice * 0.92; riskLevel = '中等';
                } else if (score >= 1) {
                    recommendation = '买入'; targetPrice = currentPrice * 1.10; stopLoss = currentPrice * 0.95; riskLevel = '中等';
                } else if (score <= -3) {
                    recommendation = '强烈卖出'; targetPrice = currentPrice * 0.85; stopLoss = currentPrice * 1.08; riskLevel = '高';
                } else if (score <= -1) {
                    recommendation = '卖出'; targetPrice = currentPrice * 0.90; stopLoss = currentPrice * 1.05; riskLevel = '中等';
                } else {
                    recommendation = '持有'; targetPrice = currentPrice * 1.05; stopLoss = currentPrice * 0.95; riskLevel = '低';
                }

                return {
                    recommendation, score, signals,
                    targetPrice: parseFloat(targetPrice.toFixed(2)),
                    stopLoss: parseFloat(stopLoss.toFixed(2)),
                    riskLevel,
                    confidence: Math.min(0.95, 0.6 + Math.abs(score) * 0.1),
                    reasoning: signals.join('; ')
                };
            }

            addDays(date, days) {
                const result = new Date(date);
                result.setDate(result.getDate() + days);
                return result;
            }
        }

        // 主要组件
        const StockAnalyzer = () => {
            const [searchTerm, setSearchTerm] = useState('');
            const [searchResults, setSearchResults] = useState([]);
            const [selectedStock, setSelectedStock] = useState(null);
            const [stockData, setStockData] = useState(null);
            const [historicalData, setHistoricalData] = useState([]);
            const [technicalIndicators, setTechnicalIndicators] = useState(null);
            const [predictions, setPredictions] = useState(null);
            const [investmentAdvice, setInvestmentAdvice] = useState(null);
            const [activeTab, setActiveTab] = useState('overview');
            const [isLoading, setIsLoading] = useState(false);
            const [isSearching, setIsSearching] = useState(false);
            const [isPredicting, setIsPredicting] = useState(false);
            const [showSuggestions, setShowSuggestions] = useState(false);

            const stockService = useRef(new StockService()).current;
            const aiEngine = useRef(new AIEngine()).current;

            const handleSearch = async () => {
                if (!searchTerm.trim()) {
                    alert('请输入股票代码或公司名称');
                    return;
                }

                setIsSearching(true);
                setShowSuggestions(false);

                const results = stockService.searchStocks(searchTerm);

                if (results.length === 0) {
                    alert(`未找到相关股票: "${searchTerm}"`);
                    setIsSearching(false);
                    return;
                }

                if (results.length === 1) {
                    await selectStock(results[0]);
                } else {
                    setSearchResults(results);
                    setShowSuggestions(true);
                }
                setIsSearching(false);
            };

            const selectStock = async (stock) => {
                setSelectedStock(stock);
                setIsLoading(true);
                setShowSuggestions(false);
                setActiveTab('overview');
                setPredictions(null);
                setInvestmentAdvice(null);

                try {
                    const realTimeData = await stockService.getRealTimeData(stock.symbol);
                    setStockData(realTimeData);

                    const historical = await stockService.getHistoricalData(stock.symbol, '1y');
                    setHistoricalData(historical);

                    const indicators = aiEngine.calculateTechnicalIndicators(historical);
                    setTechnicalIndicators(indicators);
                } catch (error) {
                    console.error('获取股票数据失败:', error);
                    alert('获取股票数据失败，请稍后重试');
                } finally {
                    setIsLoading(false);
                }
            };

            const runAIPrediction = async () => {
                if (!historicalData || historicalData.length === 0) {
                    alert('需要历史数据才能进行AI预测');
                    return;
                }

                setIsPredicting(true);

                try {
                    const predictionResults = await aiEngine.runLSTMPrediction(historicalData, 10);
                    setPredictions(predictionResults);

                    if (stockData && technicalIndicators) {
                        const advice = aiEngine.generateInvestmentAdvice(stockData, technicalIndicators, predictionResults);
                        setInvestmentAdvice(advice);
                    }
                } catch (error) {
                    console.error('AI预测失败:', error);
                    alert(`AI预测失败: ${error.message}`);
                } finally {
                    setIsPredicting(false);
                }
            };

            const handleSearchInputChange = (e) => {
                const value = e.target.value;
                setSearchTerm(value);

                if (value.length >= 2) {
                    const results = stockService.searchStocks(value);
                    setSearchResults(results.slice(0, 5));
                    setShowSuggestions(results.length > 0);
                } else {
                    setShowSuggestions(false);
                }
            };

            const handleKeyPress = (e) => {
                if (e.key === 'Enter') handleSearch();
                else if (e.key === 'Escape') setShowSuggestions(false);
            };

            return React.createElement('div', { style: { padding: '20px' } }, [
                React.createElement('div', { key: 'container', style: { maxWidth: '1400px', margin: '0 auto' } }, [
                    // Header
                    React.createElement('div', { key: 'header', className: 'text-center', style: { marginBottom: '32px' } }, [
                        React.createElement('h1', {
                            key: 'title',
                            style: { fontSize: '42px', fontWeight: 'bold', color: 'white', margin: '0 0 16px 0', textShadow: '0 2px 4px rgba(0,0,0,0.3)' }
                        }, '🌍 全球股票分析工具'),
                        React.createElement('p', {
                            key: 'subtitle',
                            style: { color: 'rgba(255,255,255,0.9)', fontSize: '18px', margin: 0, textShadow: '0 1px 2px rgba(0,0,0,0.3)' }
                        }, '基于LSTM深度学习的全球股票实时分析与AI预测系统')
                    ]),

                    // Search Section
                    React.createElement('div', { key: 'search', className: 'card', style: { padding: '32px', marginBottom: '32px', position: 'relative' } }, [
                        React.createElement('h3', {
                            key: 'title',
                            style: { fontSize: '24px', fontWeight: 'bold', margin: '0 0 8px 0', color: '#667eea' }
                        }, '🔍 全球股票搜索'),
                        React.createElement('p', {
                            key: 'desc',
                            className: 'text-gray',
                            style: { marginBottom: '24px' }
                        }, `搜索全球任意上市公司股票，支持股票代码、公司名称、行业关键词 (数据库包含${GLOBAL_STOCKS.length}只股票)`),

                        React.createElement('div', { key: 'search-input', style: { position: 'relative' } }, [
                            React.createElement('div', { key: 'input-row', className: 'flex', style: { gap: '16px' } }, [
                                React.createElement('div', { key: 'input-container', style: { flex: 1, position: 'relative' } }, [
                                    React.createElement('input', {
                                        key: 'input',
                                        className: 'input',
                                        type: 'text',
                                        value: searchTerm,
                                        onChange: handleSearchInputChange,
                                        onKeyDown: handleKeyPress,
                                        placeholder: '输入股票代码 (AAPL, TSLA) 或公司名称 (Apple, Tesla) 或行业 (Technology)',
                                        disabled: isSearching
                                    }),

                                    // 搜索建议
                                    showSuggestions && searchResults.length > 0 ? React.createElement('div', {
                                        key: 'suggestions',
                                        style: {
                                            position: 'absolute', top: '100%', left: 0, right: 0, backgroundColor: 'white',
                                            border: '1px solid #e5e7eb', borderRadius: '12px', boxShadow: '0 10px 25px rgba(0,0,0,0.1)',
                                            zIndex: 1000, marginTop: '4px', maxHeight: '300px', overflowY: 'auto'
                                        }
                                    }, searchResults.map((result, index) =>
                                        React.createElement('div', {
                                            key: result.symbol,
                                            className: 'suggestion-item',
                                            onClick: () => selectStock(result),
                                            style: { borderBottom: index < searchResults.length - 1 ? '1px solid #f3f4f6' : 'none' }
                                        }, [
                                            React.createElement('div', { key: 'main', className: 'flex-between' }, [
                                                React.createElement('div', { key: 'info' }, [
                                                    React.createElement('div', { key: 'symbol', style: { fontWeight: 'bold', fontSize: '16px', color: '#111827' } }, result.symbol),
                                                    React.createElement('div', { key: 'name', className: 'text-gray', style: { fontSize: '14px', marginTop: '2px' } }, result.name),
                                                    React.createElement('div', { key: 'details', style: { color: '#9ca3af', fontSize: '12px', marginTop: '4px' } }, `${result.exchange} • ${result.region} • ${result.sector}`)
                                                ]),
                                                React.createElement('div', {
                                                    key: 'currency',
                                                    style: { backgroundColor: '#667eea', color: 'white', padding: '4px 8px', borderRadius: '6px', fontSize: '12px' }
                                                }, result.currency)
                                            ])
                                        ])
                                    )) : null
                                ]),

                                React.createElement('button', {
                                    key: 'search-btn',
                                    className: `btn btn-primary ${isSearching || !searchTerm.trim() ? '' : ''}`,
                                    onClick: handleSearch,
                                    disabled: isSearching || !searchTerm.trim(),
                                    style: { minWidth: '120px' }
                                }, [
                                    isSearching ? React.createElement('div', { key: 'spinner', className: 'spinner', style: { marginRight: '8px' } }) : null,
                                    isSearching ? '搜索中...' : '搜索'
                                ])
                            ])
                        ])
                    ]),

                    // 股票信息展示
                    selectedStock && stockData ? [
                        // 股票卡片
                        React.createElement('div', { key: 'stock-card', className: 'card', style: { padding: '24px', marginBottom: '24px' } }, [
                            React.createElement('div', { key: 'header', className: 'flex-between', style: { marginBottom: '20px' } }, [
                                React.createElement('div', { key: 'info' }, [
                                    React.createElement('h2', {
                                        key: 'name',
                                        className: 'gradient-text',
                                        style: { fontSize: '28px', fontWeight: 'bold', margin: '0 0 8px 0' }
                                    }, stockData.name),
                                    React.createElement('div', { key: 'details', className: 'flex', style: { gap: '12px', alignItems: 'center', flexWrap: 'wrap' } }, [
                                        React.createElement('span', {
                                            key: 'symbol',
                                            style: { backgroundColor: '#667eea', color: 'white', padding: '4px 8px', borderRadius: '6px', fontSize: '14px', fontWeight: 'bold' }
                                        }, stockData.symbol),
                                        React.createElement('span', { key: 'exchange', className: 'text-gray', style: { fontSize: '14px' } }, stockData.exchange),
                                        React.createElement('span', { key: 'currency', className: 'text-gray', style: { fontSize: '14px' } }, stockData.currency),
                                        React.createElement('span', {
                                            key: 'source',
                                            style: { backgroundColor: '#10b981', color: 'white', padding: '2px 6px', borderRadius: '4px', fontSize: '12px' }
                                        }, stockData.dataSource)
                                    ])
                                ]),
                                React.createElement('div', { key: 'price', style: { textAlign: 'right' } }, [
                                    React.createElement('div', {
                                        key: 'current',
                                        style: { fontSize: '36px', fontWeight: 'bold', color: '#111827' }
                                    }, `$${stockData.price}`),
                                    React.createElement('div', {
                                        key: 'change',
                                        className: `flex ${stockData.changePercent >= 0 ? 'text-green' : 'text-red'}`,
                                        style: { alignItems: 'center', justifyContent: 'flex-end', fontSize: '16px', fontWeight: '600' }
                                    }, `${stockData.change >= 0 ? '+' : ''}${stockData.change} (${stockData.changePercent >= 0 ? '+' : ''}${stockData.changePercent}%)`)
                                ])
                            ]),
                            React.createElement('div', { key: 'stats', className: 'grid grid-3', style: { padding: '16px', backgroundColor: 'rgba(103, 126, 234, 0.05)', borderRadius: '12px' } }, [
                                React.createElement('div', { key: 'cap' }, [
                                    React.createElement('div', { className: 'text-gray', style: { fontSize: '14px' } }, '市值'),
                                    React.createElement('div', { style: { fontWeight: '700', fontSize: '16px' } }, `$${(stockData.marketCap / 1e9).toFixed(1)}B`)
                                ]),
                                React.createElement('div', { key: 'pe' }, [
                                    React.createElement('div', { className: 'text-gray', style: { fontSize: '14px' } }, 'P/E比率'),
                                    React.createElement('div', { style: { fontWeight: '700', fontSize: '16px' } }, stockData.pe)
                                ]),
                                React.createElement('div', { key: 'volume' }, [
                                    React.createElement('div', { className: 'text-gray', style: { fontSize: '14px' } }, '成交量'),
                                    React.createElement('div', { style: { fontWeight: '700', fontSize: '16px' } }, `${(stockData.volume / 1e6).toFixed(1)}M`)
                                ]),
                                React.createElement('div', { key: 'range' }, [
                                    React.createElement('div', { className: 'text-gray', style: { fontSize: '14px' } }, '52周区间'),
                                    React.createElement('div', { style: { fontWeight: '700', fontSize: '16px' } }, `$${stockData.low52} - $${stockData.high52}`)
                                ]),
                                React.createElement('div', { key: 'sector' }, [
                                    React.createElement('div', { className: 'text-gray', style: { fontSize: '14px' } }, '行业'),
                                    React.createElement('div', { style: { fontWeight: '700', fontSize: '16px' } }, stockData.sector)
                                ]),
                                React.createElement('div', { key: 'update' }, [
                                    React.createElement('div', { className: 'text-gray', style: { fontSize: '14px' } }, '更新时间'),
                                    React.createElement('div', { style: { fontWeight: '700', fontSize: '16px' } }, new Date(stockData.lastUpdate).toLocaleTimeString())
                                ])
                            ])
                        ]),

                        // 标签页
                        React.createElement('div', { key: 'tabs', className: 'card', style: { padding: '0', marginBottom: '24px', overflow: 'hidden' } }, [
                            React.createElement('div', { key: 'tab-nav', className: 'flex', style: { borderBottom: '1px solid #e5e7eb', backgroundColor: '#f8fafc' } }, [
                                { id: 'overview', label: '📊 概览' },
                                { id: 'technical', label: '📈 技术分析' },
                                { id: 'prediction', label: '🧠 AI预测' },
                                { id: 'advice', label: '💡 投资建议' }
                            ].map(tab =>
                                React.createElement('button', {
                                    key: tab.id,
                                    className: `tab ${activeTab === tab.id ? 'active' : ''}`,
                                    onClick: () => setActiveTab(tab.id)
                                }, tab.label)
                            ))
                        ]),

                        // 标签页内容
                        React.createElement('div', { key: 'tab-content', className: 'card', style: { padding: '32px' } }, [
                            // 概览标签页
                            activeTab === 'overview' ? React.createElement('div', { key: 'overview' }, [
                                React.createElement('h3', { key: 'title', style: { fontSize: '24px', fontWeight: 'bold', marginBottom: '24px' } }, '📊 股票概览'),

                                React.createElement('div', { key: 'chart', style: { marginBottom: '32px' } }, [
                                    React.createElement('h4', { key: 'chart-title', style: { fontSize: '18px', fontWeight: '600', marginBottom: '16px' } }, '价格走势 (最近90天)'),
                                    React.createElement(ResponsiveContainer, { key: 'chart-container', width: '100%', height: 300 },
                                        React.createElement(LineChart, { data: historicalData.slice(-90) }, [
                                            React.createElement(CartesianGrid, { key: 'grid', strokeDasharray: '3 3' }),
                                            React.createElement(XAxis, { key: 'x', dataKey: 'date', tick: { fontSize: 12 } }),
                                            React.createElement(YAxis, { key: 'y', tick: { fontSize: 12 } }),
                                            React.createElement(Tooltip, { key: 'tooltip' }),
                                            React.createElement(Line, { key: 'line', type: 'monotone', dataKey: 'close', stroke: '#667eea', strokeWidth: 2, dot: false })
                                        ])
                                    )
                                ]),

                                React.createElement('div', { key: 'volume' }, [
                                    React.createElement('h4', { key: 'volume-title', style: { fontSize: '18px', fontWeight: '600', marginBottom: '16px' } }, '成交量 (最近7天)'),
                                    React.createElement(ResponsiveContainer, { key: 'volume-container', width: '100%', height: 200 },
                                        React.createElement(BarChart, { data: historicalData.slice(-7) }, [
                                            React.createElement(CartesianGrid, { key: 'grid', strokeDasharray: '3 3' }),
                                            React.createElement(XAxis, { key: 'x', dataKey: 'date', tick: { fontSize: 12 } }),
                                            React.createElement(YAxis, { key: 'y', tick: { fontSize: 12 } }),
                                            React.createElement(Tooltip, { key: 'tooltip' }),
                                            React.createElement(Bar, { key: 'bar', dataKey: 'volume', fill: '#667eea' })
                                        ])
                                    )
                                ])
                            ]) : null,

                            // 技术分析标签页
                            activeTab === 'technical' && technicalIndicators ? React.createElement('div', { key: 'technical' }, [
                                React.createElement('h3', { key: 'title', style: { fontSize: '24px', fontWeight: 'bold', marginBottom: '24px' } }, '📈 技术分析'),

                                React.createElement('div', { key: 'indicators', className: 'grid grid-2' }, [
                                    React.createElement('div', { key: 'sma', style: { padding: '20px', backgroundColor: '#f8fafc', borderRadius: '12px' } }, [
                                        React.createElement('h4', { style: { margin: '0 0 12px 0', color: '#374151' } }, '移动平均线'),
                                        React.createElement('div', { style: { fontSize: '14px', color: '#6b7280' } }, [
                                            React.createElement('div', { key: 'sma20' }, `SMA20: $${technicalIndicators.sma.sma20}`),
                                            React.createElement('div', { key: 'sma50' }, `SMA50: $${technicalIndicators.sma.sma50}`)
                                        ])
                                    ]),
                                    React.createElement('div', { key: 'rsi', style: { padding: '20px', backgroundColor: '#f8fafc', borderRadius: '12px' } }, [
                                        React.createElement('h4', { style: { margin: '0 0 12px 0', color: '#374151' } }, 'RSI指标'),
                                        React.createElement('div', {
                                            style: {
                                                fontSize: '24px',
                                                fontWeight: 'bold',
                                                color: technicalIndicators.rsi < 30 ? '#059669' : technicalIndicators.rsi > 70 ? '#dc2626' : '#6b7280'
                                            }
                                        }, technicalIndicators.rsi),
                                        React.createElement('div', {
                                            style: { fontSize: '12px', color: '#6b7280', marginTop: '4px' }
                                        }, technicalIndicators.rsi < 30 ? '超卖' : technicalIndicators.rsi > 70 ? '超买' : '中性')
                                    ]),
                                    React.createElement('div', { key: 'macd', style: { padding: '20px', backgroundColor: '#f8fafc', borderRadius: '12px' } }, [
                                        React.createElement('h4', { style: { margin: '0 0 12px 0', color: '#374151' } }, 'MACD'),
                                        React.createElement('div', { style: { fontSize: '14px', color: '#6b7280' } }, [
                                            React.createElement('div', { key: 'macd' }, `MACD: ${technicalIndicators.macd.macd}`),
                                            React.createElement('div', { key: 'signal' }, `信号线: ${technicalIndicators.macd.signal}`)
                                        ])
                                    ])
                                ])
                            ]) : null,

                            // AI预测标签页
                            activeTab === 'prediction' ? React.createElement('div', { key: 'prediction' }, [
                                React.createElement('div', { key: 'header', className: 'flex-between', style: { marginBottom: '24px' } }, [
                                    React.createElement('h3', { key: 'title', style: { fontSize: '24px', fontWeight: 'bold', margin: 0 } }, '🧠 LSTM AI预测'),
                                    React.createElement('button', {
                                        key: 'predict-btn',
                                        className: 'btn btn-primary',
                                        onClick: runAIPrediction,
                                        disabled: isPredicting
                                    }, isPredicting ? '预测中...' : '运行AI预测')
                                ]),

                                isPredicting ? React.createElement('div', { key: 'loading', className: 'text-center', style: { padding: '48px' } }, [
                                    React.createElement('div', {
                                        key: 'spinner',
                                        style: {
                                            width: '48px', height: '48px', border: '4px solid #f3f4f6', borderTop: '4px solid #667eea',
                                            borderRadius: '50%', animation: 'spin 1s linear infinite', margin: '0 auto 16px'
                                        }
                                    }),
                                    React.createElement('p', { key: 'text', className: 'text-gray', style: { fontSize: '16px' } }, '正在运行LSTM神经网络分析...')
                                ]) : null,

                                predictions && !isPredicting ? React.createElement('div', { key: 'results' }, [
                                    React.createElement('div', { key: 'chart', style: { marginBottom: '24px' } }, [
                                        React.createElement('h4', { key: 'chart-title', style: { fontSize: '18px', fontWeight: '600', marginBottom: '16px' } }, '未来10天价格预测'),
                                        React.createElement(ResponsiveContainer, { key: 'chart-container', width: '100%', height: 250 },
                                            React.createElement(LineChart, { data: predictions.predictions }, [
                                                React.createElement(CartesianGrid, { key: 'grid', strokeDasharray: '3 3' }),
                                                React.createElement(XAxis, { key: 'x', dataKey: 'day' }),
                                                React.createElement(YAxis, { key: 'y' }),
                                                React.createElement(Tooltip, { key: 'tooltip' }),
                                                React.createElement(Line, { key: 'price', type: 'monotone', dataKey: 'predictedPrice', stroke: '#8b5cf6', strokeWidth: 3 })
                                            ])
                                        )
                                    ]),
                                    React.createElement('div', { key: 'prediction-grid', className: 'grid', style: { gridTemplateColumns: 'repeat(auto-fit, minmax(100px, 1fr))', gap: '12px' } },
                                        predictions.predictions.slice(0, 5).map((pred, idx) =>
                                            React.createElement('div', {
                                                key: idx,
                                                className: 'prediction-card'
                                            }, [
                                                React.createElement('div', { key: 'day', className: 'text-gray', style: { fontSize: '12px', marginBottom: '4px' } }, `第${pred.day}天`),
                                                React.createElement('div', { key: 'price', style: { fontWeight: 'bold', fontSize: '16px', color: '#111827', marginBottom: '4px' } }, `$${pred.predictedPrice}`),
                                                React.createElement('div', { key: 'confidence', className: 'text-green', style: { fontSize: '12px' } }, `${(pred.confidence * 100).toFixed(0)}%`),
                                                React.createElement('div', {
                                                    key: 'change',
                                                    className: pred.change >= 0 ? 'text-green' : 'text-red',
                                                    style: { fontSize: '12px', marginTop: '4px' }
                                                }, `${pred.change >= 0 ? '+' : ''}${pred.change}%`)
                                            ])
                                        )
                                    )
                                ]) : null,

                                !predictions && !isPredicting ? React.createElement('div', {
                                    key: 'empty',
                                    className: 'text-center text-gray',
                                    style: { padding: '48px' }
                                }, '点击"运行AI预测"开始分析') : null
                            ]) : null,

                            // 投资建议标签页
                            activeTab === 'advice' && investmentAdvice ? React.createElement('div', { key: 'advice' }, [
                                React.createElement('h3', { key: 'title', style: { fontSize: '24px', fontWeight: 'bold', marginBottom: '24px' } }, '💡 投资建议'),

                                React.createElement('div', {
                                    key: 'recommendation',
                                    className: investmentAdvice.recommendation.includes('买入') ? 'bg-green' :
                                              investmentAdvice.recommendation.includes('卖出') ? 'bg-red' : 'bg-gray',
                                    style: { padding: '24px', borderRadius: '12px', marginBottom: '24px' }
                                }, [
                                    React.createElement('div', { key: 'main', className: 'text-center', style: { marginBottom: '16px' } }, [
                                        React.createElement('div', {
                                            key: 'action',
                                            style: {
                                                fontSize: '32px',
                                                fontWeight: 'bold',
                                                color: investmentAdvice.recommendation.includes('买入') ? '#059669' :
                                                       investmentAdvice.recommendation.includes('卖出') ? '#dc2626' : '#6b7280'
                                            }
                                        }, investmentAdvice.recommendation),
                                        React.createElement('div', {
                                            key: 'confidence',
                                            className: 'text-gray',
                                            style: { fontSize: '14px', marginTop: '8px' }
                                        }, `置信度: ${(investmentAdvice.confidence * 100).toFixed(0)}% | 风险等级: ${investmentAdvice.riskLevel}`)
                                    ]),
                                    React.createElement('div', { key: 'details', className: 'grid grid-3' }, [
                                        React.createElement('div', { key: 'target' }, [
                                            React.createElement('div', { className: 'text-gray', style: { fontSize: '12px' } }, '目标价位'),
                                            React.createElement('div', { style: { fontSize: '18px', fontWeight: 'bold' } }, `$${investmentAdvice.targetPrice}`)
                                        ]),
                                        React.createElement('div', { key: 'stop' }, [
                                            React.createElement('div', { className: 'text-gray', style: { fontSize: '12px' } }, '止损价位'),
                                            React.createElement('div', { style: { fontSize: '18px', fontWeight: 'bold' } }, `$${investmentAdvice.stopLoss}`)
                                        ]),
                                        React.createElement('div', { key: 'score' }, [
                                            React.createElement('div', { className: 'text-gray', style: { fontSize: '12px' } }, '综合评分'),
                                            React.createElement('div', { style: { fontSize: '18px', fontWeight: 'bold' } }, investmentAdvice.score)
                                        ])
                                    ])
                                ]),

                                React.createElement('div', { key: 'reasoning', style: { padding: '20px', backgroundColor: '#f8fafc', borderRadius: '12px' } }, [
                                    React.createElement('h4', { key: 'title', style: { margin: '0 0 12px 0', fontSize: '16px', fontWeight: '600' } }, '分析依据'),
                                    React.createElement('div', { key: 'signals', style: { fontSize: '14px', color: '#374151', lineHeight: '1.6' } },
                                        investmentAdvice.signals.map((signal, idx) =>
                                            React.createElement('div', { key: idx, style: { marginBottom: '4px' } }, `• ${signal}`)
                                        )
                                    )
                                ])
                            ]) : null,

                            activeTab === 'advice' && !investmentAdvice ? React.createElement('div', {
                                key: 'advice-empty',
                                className: 'text-center text-gray',
                                style: { padding: '48px' }
                            }, '请先运行AI预测以获取投资建议') : null
                        ])
                    ] : null,

                    // 加载状态
                    isLoading ? React.createElement('div', { key: 'loading', className: 'card text-center', style: { padding: '48px' } }, [
                        React.createElement('div', {
                            key: 'spinner',
                            style: {
                                width: '48px', height: '48px', border: '4px solid #f3f4f6', borderTop: '4px solid #667eea',
                                borderRadius: '50%', animation: 'spin 1s linear infinite', margin: '0 auto 16px'
                            }
                        }),
                        React.createElement('p', { key: 'text', className: 'text-gray', style: { fontSize: '16px' } }, '正在获取股票数据...')
                    ]) : null,

                    // 空状态
                    !selectedStock && !isLoading ? React.createElement('div', { key: 'empty', className: 'card text-center', style: { padding: '64px' } }, [
                        React.createElement('div', { key: 'icon', style: { fontSize: '72px', marginBottom: '24px' } }, '🔍'),
                        React.createElement('h3', { key: 'title', style: { fontSize: '24px', fontWeight: '600', color: '#374151', marginBottom: '12px' } }, '开始全球股票分析'),
                        React.createElement('p', { key: 'desc', className: 'text-gray', style: { fontSize: '16px', marginBottom: '24px' } }, '在上方搜索框中输入任意股票代码或公司名称开始分析'),
                        React.createElement('div', { key: 'examples', className: 'flex-center', style: { gap: '12px', flexWrap: 'wrap' } },
                            ['AAPL', 'TSLA', 'MSFT', 'GOOGL', 'BABA', 'NVDA'].map(symbol =>
                                React.createElement('button', {
                                    key: symbol,
                                    className: 'btn btn-primary',
                                    onClick: () => {
                                        setSearchTerm(symbol);
                                        setTimeout(() => handleSearch(), 100);
                                    },
                                    style: { padding: '8px 16px', borderRadius: '20px', fontSize: '14px' }
                                }, symbol)
                            )
                        )
                    ]) : null
                ])
            ]);
        };

        ReactDOM.render(React.createElement(StockAnalyzer), document.getElementById('root'));
    </script>
</body>
</html>
