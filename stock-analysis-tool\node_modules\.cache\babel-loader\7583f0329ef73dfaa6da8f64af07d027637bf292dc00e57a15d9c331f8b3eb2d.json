{"ast": null, "code": "import { <PERSON><PERSON><PERSON> } from \"./cardinalOpen.js\";\nimport { point } from \"./catmullRom.js\";\nfunction CatmullRomOpen(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\nCatmullRomOpen.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;\n    this._l01_a = this._l12_a = this._l23_a = this._l01_2a = this._l12_2a = this._l23_2a = this._point = 0;\n  },\n  lineEnd: function () {\n    if (this._line || this._line !== 0 && this._point === 3) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    if (this._point) {\n      var x23 = this._x2 - x,\n        y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        break;\n      case 1:\n        this._point = 2;\n        break;\n      case 2:\n        this._point = 3;\n        this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2);\n        break;\n      case 3:\n        this._point = 4;\n      // falls through\n      default:\n        point(this, x, y);\n        break;\n    }\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\nexport default (function custom(alpha) {\n  function catmullRom(context) {\n    return alpha ? new CatmullRomOpen(context, alpha) : new CardinalOpen(context, 0);\n  }\n  catmullRom.alpha = function (alpha) {\n    return custom(+alpha);\n  };\n  return catmullRom;\n})(0.5);", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "point", "CatmullRomOpen", "context", "alpha", "_context", "_alpha", "prototype", "areaStart", "_line", "areaEnd", "NaN", "lineStart", "_x0", "_x1", "_x2", "_y0", "_y1", "_y2", "_l01_a", "_l12_a", "_l23_a", "_l01_2a", "_l12_2a", "_l23_2a", "_point", "lineEnd", "closePath", "x", "y", "x23", "y23", "Math", "sqrt", "pow", "lineTo", "moveTo", "custom", "catmullRom"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/d3-shape/src/curve/catmullRomOpen.js"], "sourcesContent": ["import {<PERSON><PERSON><PERSON>} from \"./cardinalOpen.js\";\nimport {point} from \"./catmullRom.js\";\n\nfunction CatmullRomOpen(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\n\nCatmullRomOpen.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._l01_a = this._l12_a = this._l23_a =\n    this._l01_2a = this._l12_2a = this._l23_2a =\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line || (this._line !== 0 && this._point === 3)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n\n    if (this._point) {\n      var x23 = this._x2 - x,\n          y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n\n    switch (this._point) {\n      case 0: this._point = 1; break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; this._line ? this._context.lineTo(this._x2, this._y2) : this._context.moveTo(this._x2, this._y2); break;\n      case 3: this._point = 4; // falls through\n      default: point(this, x, y); break;\n    }\n\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(alpha) {\n\n  function catmullRom(context) {\n    return alpha ? new CatmullRomOpen(context, alpha) : new CardinalOpen(context, 0);\n  }\n\n  catmullRom.alpha = function(alpha) {\n    return custom(+alpha);\n  };\n\n  return catmullRom;\n})(0.5);\n"], "mappings": "AAAA,SAAQA,YAAY,QAAO,mBAAmB;AAC9C,SAAQC,KAAK,QAAO,iBAAiB;AAErC,SAASC,cAAcA,CAACC,OAAO,EAAEC,KAAK,EAAE;EACtC,IAAI,CAACC,QAAQ,GAAGF,OAAO;EACvB,IAAI,CAACG,MAAM,GAAGF,KAAK;AACrB;AAEAF,cAAc,CAACK,SAAS,GAAG;EACzBC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,KAAK,GAAG,CAAC;EAChB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAI,CAACD,KAAK,GAAGE,GAAG;EAClB,CAAC;EACDC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAC9B,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,GAAG,GAAGP,GAAG;IACpC,IAAI,CAACQ,MAAM,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI,CAACC,MAAM,GACvC,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,OAAO,GAC1C,IAAI,CAACC,MAAM,GAAG,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAI,IAAI,CAACjB,KAAK,IAAK,IAAI,CAACA,KAAK,KAAK,CAAC,IAAI,IAAI,CAACgB,MAAM,KAAK,CAAE,EAAE,IAAI,CAACpB,QAAQ,CAACsB,SAAS,CAAC,CAAC;IACpF,IAAI,CAAClB,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK;EAC7B,CAAC;EACDR,KAAK,EAAE,SAAAA,CAAS2B,CAAC,EAAEC,CAAC,EAAE;IACpBD,CAAC,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC;IAEd,IAAI,IAAI,CAACJ,MAAM,EAAE;MACf,IAAIK,GAAG,GAAG,IAAI,CAACf,GAAG,GAAGa,CAAC;QAClBG,GAAG,GAAG,IAAI,CAACb,GAAG,GAAGW,CAAC;MACtB,IAAI,CAACR,MAAM,GAAGW,IAAI,CAACC,IAAI,CAAC,IAAI,CAACT,OAAO,GAAGQ,IAAI,CAACE,GAAG,CAACJ,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG,EAAE,IAAI,CAACzB,MAAM,CAAC,CAAC;IACtF;IAEA,QAAQ,IAAI,CAACmB,MAAM;MACjB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;QAAE;MACzB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;QAAE;MACzB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;QAAE,IAAI,CAAChB,KAAK,GAAG,IAAI,CAACJ,QAAQ,CAAC8B,MAAM,CAAC,IAAI,CAACpB,GAAG,EAAE,IAAI,CAACG,GAAG,CAAC,GAAG,IAAI,CAACb,QAAQ,CAAC+B,MAAM,CAAC,IAAI,CAACrB,GAAG,EAAE,IAAI,CAACG,GAAG,CAAC;QAAE;MAC3H,KAAK,CAAC;QAAE,IAAI,CAACO,MAAM,GAAG,CAAC;MAAE;MACzB;QAASxB,KAAK,CAAC,IAAI,EAAE2B,CAAC,EAAEC,CAAC,CAAC;QAAE;IAC9B;IAEA,IAAI,CAACV,MAAM,GAAG,IAAI,CAACC,MAAM,EAAE,IAAI,CAACA,MAAM,GAAG,IAAI,CAACC,MAAM;IACpD,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,OAAO,EAAE,IAAI,CAACA,OAAO,GAAG,IAAI,CAACC,OAAO;IACxD,IAAI,CAACX,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGa,CAAC;IACtD,IAAI,CAACZ,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAG,IAAI,CAACC,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGW,CAAC;EACxD;AACF,CAAC;AAED,eAAe,CAAC,SAASQ,MAAMA,CAACjC,KAAK,EAAE;EAErC,SAASkC,UAAUA,CAACnC,OAAO,EAAE;IAC3B,OAAOC,KAAK,GAAG,IAAIF,cAAc,CAACC,OAAO,EAAEC,KAAK,CAAC,GAAG,IAAIJ,YAAY,CAACG,OAAO,EAAE,CAAC,CAAC;EAClF;EAEAmC,UAAU,CAAClC,KAAK,GAAG,UAASA,KAAK,EAAE;IACjC,OAAOiC,MAAM,CAAC,CAACjC,KAAK,CAAC;EACvB,CAAC;EAED,OAAOkC,UAAU;AACnB,CAAC,EAAE,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}