{"ast": null, "code": "import formatLocale from \"./locale.js\";\nvar locale;\nexport var format;\nexport var formatPrefix;\ndefaultLocale({\n  thousands: \",\",\n  grouping: [3],\n  currency: [\"$\", \"\"]\n});\nexport default function defaultLocale(definition) {\n  locale = formatLocale(definition);\n  format = locale.format;\n  formatPrefix = locale.formatPrefix;\n  return locale;\n}", "map": {"version": 3, "names": ["formatLocale", "locale", "format", "formatPrefix", "defaultLocale", "thousands", "grouping", "currency", "definition"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/d3-format/src/defaultLocale.js"], "sourcesContent": ["import formatLocale from \"./locale.js\";\n\nvar locale;\nexport var format;\nexport var formatPrefix;\n\ndefaultLocale({\n  thousands: \",\",\n  grouping: [3],\n  currency: [\"$\", \"\"]\n});\n\nexport default function defaultLocale(definition) {\n  locale = formatLocale(definition);\n  format = locale.format;\n  formatPrefix = locale.formatPrefix;\n  return locale;\n}\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,aAAa;AAEtC,IAAIC,MAAM;AACV,OAAO,IAAIC,MAAM;AACjB,OAAO,IAAIC,YAAY;AAEvBC,aAAa,CAAC;EACZC,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,CAAC,CAAC,CAAC;EACbC,QAAQ,EAAE,CAAC,GAAG,EAAE,EAAE;AACpB,CAAC,CAAC;AAEF,eAAe,SAASH,aAAaA,CAACI,UAAU,EAAE;EAChDP,MAAM,GAAGD,YAAY,CAACQ,UAAU,CAAC;EACjCN,MAAM,GAAGD,MAAM,CAACC,MAAM;EACtBC,YAAY,GAAGF,MAAM,CAACE,YAAY;EAClC,OAAOF,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}