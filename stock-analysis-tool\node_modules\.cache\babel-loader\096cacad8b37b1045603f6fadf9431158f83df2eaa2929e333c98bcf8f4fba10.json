{"ast": null, "code": "var freeGlobal = require('./_freeGlobal');\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\nmodule.exports = root;", "map": {"version": 3, "names": ["freeGlobal", "require", "freeSelf", "self", "Object", "root", "Function", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/lodash/_root.js"], "sourcesContent": ["var freeGlobal = require('./_freeGlobal');\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\nmodule.exports = root;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAEzC;AACA,IAAIC,QAAQ,GAAG,OAAOC,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAKA,MAAM,IAAID,IAAI;;AAEhF;AACA,IAAIE,IAAI,GAAGL,UAAU,IAAIE,QAAQ,IAAII,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;AAE9DC,MAAM,CAACC,OAAO,GAAGH,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}