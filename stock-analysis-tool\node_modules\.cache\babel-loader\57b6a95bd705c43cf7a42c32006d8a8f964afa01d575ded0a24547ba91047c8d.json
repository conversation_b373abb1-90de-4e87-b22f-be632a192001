{"ast": null, "code": "var getNative = require('./_getNative'),\n  root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar DataView = getNative(root, 'DataView');\nmodule.exports = DataView;", "map": {"version": 3, "names": ["getNative", "require", "root", "DataView", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/基于LSTM时间序列预测/工具/ag/stock-analysis-tool/node_modules/lodash/_DataView.js"], "sourcesContent": ["var getNative = require('./_getNative'),\n    root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar DataView = getNative(root, 'DataView');\n\nmodule.exports = DataView;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;EACnCC,IAAI,GAAGD,OAAO,CAAC,SAAS,CAAC;;AAE7B;AACA,IAAIE,QAAQ,GAAGH,SAAS,CAACE,IAAI,EAAE,UAAU,CAAC;AAE1CE,MAAM,CAACC,OAAO,GAAGF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}