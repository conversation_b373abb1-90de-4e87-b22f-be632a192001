<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票分析工具 - 演示版</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://unpkg.com/recharts@2.7.2/umd/Recharts.js"></script>
    <script src="https://unpkg.com/lucide-react@0.263.1/dist/umd/lucide-react.js"></script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="root"></div>
    
    <script type="text/babel">
        const { useState } = React;
        const { Search, ArrowUp, ArrowDown, TrendingUp, Brain, BarChart3, Wifi, WifiOff } = lucideReact;
        const { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } = Recharts;

        const StockAnalysisTool = () => {
            const [searchTerm, setSearchTerm] = useState('');
            const [selectedStock, setSelectedStock] = useState(null);
            const [isLoading, setIsLoading] = useState(false);
            const [predictions, setPredictions] = useState(null);
            const [isOnline, setIsOnline] = useState(navigator.onLine);

            // 模拟股票数据
            const mockStocks = {
                'AAPL': {
                    symbol: 'AAPL',
                    name: 'Apple Inc.',
                    price: 185.75,
                    change: 2.35,
                    changePercent: 1.28,
                    volume: 45680000,
                    marketCap: 2890000000000,
                    pe: 28.5,
                    high52: 198.23,
                    low52: 124.17,
                    sector: 'Technology',
                    industry: 'Consumer Electronics'
                },
                'TSLA': {
                    symbol: 'TSLA',
                    name: 'Tesla, Inc.',
                    price: 248.50,
                    change: -8.45,
                    changePercent: -3.29,
                    volume: 98750000,
                    marketCap: 789000000000,
                    pe: 65.2,
                    high52: 299.29,
                    low52: 138.80,
                    sector: 'Consumer Cyclical',
                    industry: 'Auto Manufacturers'
                },
                'MSFT': {
                    symbol: 'MSFT',
                    name: 'Microsoft Corporation',
                    price: 378.85,
                    change: 5.20,
                    changePercent: 1.39,
                    volume: 28450000,
                    marketCap: 2810000000000,
                    pe: 32.1,
                    high52: 384.30,
                    low52: 309.45,
                    sector: 'Technology',
                    industry: 'Software'
                },
                'GOOGL': {
                    symbol: 'GOOGL',
                    name: 'Alphabet Inc.',
                    price: 141.80,
                    change: -1.25,
                    changePercent: -0.87,
                    volume: 35670000,
                    marketCap: 1750000000000,
                    pe: 24.3,
                    high52: 151.55,
                    low52: 121.46,
                    sector: 'Technology',
                    industry: 'Internet'
                },
                'NVDA': {
                    symbol: 'NVDA',
                    name: 'NVIDIA Corporation',
                    price: 485.20,
                    change: 12.45,
                    changePercent: 2.63,
                    volume: 52340000,
                    marketCap: 1200000000000,
                    pe: 68.5,
                    high52: 502.66,
                    low52: 180.96,
                    sector: 'Technology',
                    industry: 'Semiconductors'
                }
            };

            // 生成历史数据
            const generateHistoricalData = () => {
                const data = [];
                let price = selectedStock?.price || 150;
                for (let i = 30; i >= 0; i--) {
                    const date = new Date();
                    date.setDate(date.getDate() - i);
                    price += (Math.random() - 0.5) * 10;
                    data.push({
                        date: date.toISOString().split('T')[0],
                        price: parseFloat(price.toFixed(2)),
                        volume: Math.floor(Math.random() * 100000000) + 20000000
                    });
                }
                return data;
            };

            // 搜索股票
            const searchStock = (term) => {
                const upperTerm = term.toUpperCase();
                const found = mockStocks[upperTerm];
                
                if (found) {
                    setSelectedStock(found);
                } else {
                    alert(`未找到股票: ${term}\n\n可搜索的股票: ${Object.keys(mockStocks).join(', ')}`);
                }
            };

            // LSTM预测
            const runPrediction = async () => {
                if (!selectedStock) {
                    alert('请先选择一只股票');
                    return;
                }
                
                setIsLoading(true);
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                const currentPrice = selectedStock.price;
                const predictions = [];
                let predictedPrice = currentPrice;
                
                for (let i = 1; i <= 7; i++) {
                    predictedPrice += (Math.random() - 0.45) * 5;
                    predictions.push({
                        day: i,
                        predictedPrice: parseFloat(predictedPrice.toFixed(2)),
                        confidence: Math.max(0.6, 0.95 - i * 0.05)
                    });
                }
                
                setPredictions(predictions);
                setIsLoading(false);
            };

            const handleSearch = () => {
                if (searchTerm.trim()) {
                    searchStock(searchTerm.trim());
                }
            };

            const StockCard = ({ stock }) => (
                React.createElement('div', {
                    style: {
                        backgroundColor: 'white',
                        borderRadius: '12px',
                        padding: '24px',
                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                        border: '1px solid #e5e7eb',
                        marginBottom: '24px'
                    }
                }, [
                    React.createElement('div', {
                        key: 'header',
                        style: { display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '16px' }
                    }, [
                        React.createElement('div', { key: 'info' }, [
                            React.createElement('h2', { 
                                key: 'name',
                                style: { fontSize: '24px', fontWeight: 'bold', color: '#111827', margin: 0 } 
                            }, stock.name),
                            React.createElement('p', { 
                                key: 'symbol',
                                style: { color: '#6b7280', margin: '4px 0 0 0' } 
                            }, stock.symbol)
                        ]),
                        React.createElement('div', { 
                            key: 'price',
                            style: { textAlign: 'right' } 
                        }, [
                            React.createElement('div', { 
                                key: 'current',
                                style: { fontSize: '32px', fontWeight: 'bold', color: '#111827' } 
                            }, `$${stock.price}`),
                            React.createElement('div', { 
                                key: 'change',
                                style: { 
                                    display: 'flex', 
                                    alignItems: 'center', 
                                    color: stock.changePercent >= 0 ? '#059669' : '#dc2626',
                                    justifyContent: 'flex-end'
                                } 
                            }, [
                                React.createElement(stock.changePercent >= 0 ? ArrowUp : ArrowDown, { 
                                    key: 'arrow',
                                    size: 16, 
                                    style: { marginRight: '4px' } 
                                }),
                                `$${Math.abs(stock.change)} (${Math.abs(stock.changePercent)}%)`
                            ])
                        ])
                    ]),
                    React.createElement('div', {
                        key: 'stats',
                        style: { 
                            display: 'grid', 
                            gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', 
                            gap: '16px', 
                            fontSize: '14px' 
                        }
                    }, [
                        React.createElement('div', { key: 'cap' }, [
                            React.createElement('div', { style: { color: '#6b7280' } }, '市值'),
                            React.createElement('div', { style: { fontWeight: '600' } }, `$${(stock.marketCap / 1e9).toFixed(1)}B`)
                        ]),
                        React.createElement('div', { key: 'pe' }, [
                            React.createElement('div', { style: { color: '#6b7280' } }, 'P/E比率'),
                            React.createElement('div', { style: { fontWeight: '600' } }, stock.pe)
                        ]),
                        React.createElement('div', { key: 'volume' }, [
                            React.createElement('div', { style: { color: '#6b7280' } }, '成交量'),
                            React.createElement('div', { style: { fontWeight: '600' } }, `${(stock.volume / 1e6).toFixed(1)}M`)
                        ]),
                        React.createElement('div', { key: 'industry' }, [
                            React.createElement('div', { style: { color: '#6b7280' } }, '行业'),
                            React.createElement('div', { style: { fontWeight: '600' } }, stock.industry)
                        ])
                    ])
                ])
            );

            return React.createElement('div', {
                style: {
                    minHeight: '100vh',
                    background: 'linear-gradient(to bottom right, #dbeafe, #e0e7ff)',
                    padding: '16px'
                }
            }, [
                React.createElement('div', {
                    key: 'container',
                    style: { maxWidth: '1200px', margin: '0 auto' }
                }, [
                    // Header
                    React.createElement('div', {
                        key: 'header',
                        style: { textAlign: 'center', marginBottom: '32px' }
                    }, [
                        React.createElement('div', {
                            key: 'title-row',
                            style: { display: 'flex', justifyContent: 'center', alignItems: 'center', marginBottom: '16px' }
                        }, [
                            React.createElement('h1', {
                                key: 'title',
                                style: { fontSize: '36px', fontWeight: 'bold', color: '#111827', marginRight: '16px' }
                            }, '股票分析工具'),
                            React.createElement('div', {
                                key: 'status',
                                style: { display: 'flex', alignItems: 'center' }
                            }, [
                                React.createElement(isOnline ? Wifi : WifiOff, { 
                                    key: 'icon',
                                    size: 20, 
                                    color: isOnline ? '#059669' : '#dc2626' 
                                }),
                                React.createElement('span', {
                                    key: 'text',
                                    style: { fontSize: '14px', marginLeft: '4px', color: isOnline ? '#059669' : '#dc2626' }
                                }, isOnline ? '在线' : '离线')
                            ])
                        ]),
                        React.createElement('p', {
                            key: 'subtitle',
                            style: { color: '#6b7280' }
                        }, '基于LSTM深度学习的智能股票分析与预测系统')
                    ]),

                    // Search
                    React.createElement('div', {
                        key: 'search',
                        style: {
                            backgroundColor: 'white',
                            borderRadius: '12px',
                            padding: '24px',
                            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                            marginBottom: '32px'
                        }
                    }, [
                        React.createElement('div', {
                            key: 'search-row',
                            style: { display: 'flex', gap: '16px' }
                        }, [
                            React.createElement('input', {
                                key: 'input',
                                type: 'text',
                                value: searchTerm,
                                onChange: (e) => setSearchTerm(e.target.value),
                                onKeyPress: (e) => e.key === 'Enter' && handleSearch(),
                                placeholder: '输入股票代码 (如: AAPL, TSLA, MSFT, GOOGL, NVDA)',
                                style: {
                                    flex: 1,
                                    padding: '12px 16px',
                                    border: '1px solid #d1d5db',
                                    borderRadius: '8px',
                                    fontSize: '16px',
                                    outline: 'none'
                                }
                            }),
                            React.createElement('button', {
                                key: 'button',
                                onClick: handleSearch,
                                style: {
                                    backgroundColor: '#2563eb',
                                    color: 'white',
                                    padding: '12px 24px',
                                    borderRadius: '8px',
                                    border: 'none',
                                    cursor: 'pointer',
                                    display: 'flex',
                                    alignItems: 'center',
                                    fontSize: '16px'
                                }
                            }, [
                                React.createElement(Search, { key: 'icon', size: 20, style: { marginRight: '8px' } }),
                                '搜索'
                            ])
                        ]),
                        
                        React.createElement('div', {
                            key: 'shortcuts',
                            style: { marginTop: '16px', display: 'flex', flexWrap: 'wrap', gap: '8px' }
                        }, [
                            React.createElement('span', {
                                key: 'label',
                                style: { fontSize: '14px', color: '#6b7280' }
                            }, '快捷选择:'),
                            ...Object.keys(mockStocks).map(symbol =>
                                React.createElement('button', {
                                    key: symbol,
                                    onClick: () => {
                                        setSearchTerm(symbol);
                                        searchStock(symbol);
                                    },
                                    style: {
                                        backgroundColor: '#f3f4f6',
                                        border: 'none',
                                        padding: '4px 12px',
                                        borderRadius: '16px',
                                        fontSize: '14px',
                                        cursor: 'pointer'
                                    }
                                }, symbol)
                            )
                        ])
                    ]),

                    // Stock Info
                    selectedStock ? [
                        React.createElement(StockCard, { key: 'card', stock: selectedStock }),
                        
                        // Charts
                        React.createElement('div', {
                            key: 'charts',
                            style: { 
                                display: 'grid', 
                                gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', 
                                gap: '24px', 
                                marginBottom: '32px' 
                            }
                        }, [
                            // Price Chart
                            React.createElement('div', {
                                key: 'price-chart',
                                style: {
                                    backgroundColor: 'white',
                                    borderRadius: '12px',
                                    padding: '24px',
                                    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
                                }
                            }, [
                                React.createElement('h3', {
                                    key: 'title',
                                    style: { fontSize: '20px', fontWeight: 'bold', marginBottom: '16px', display: 'flex', alignItems: 'center' }
                                }, [
                                    React.createElement(TrendingUp, { key: 'icon', size: 20, color: '#2563eb', style: { marginRight: '8px' } }),
                                    '价格走势'
                                ]),
                                React.createElement(ResponsiveContainer, {
                                    key: 'chart',
                                    width: '100%',
                                    height: 250
                                }, React.createElement(LineChart, {
                                    data: generateHistoricalData()
                                }, [
                                    React.createElement(CartesianGrid, { key: 'grid', strokeDasharray: '3 3' }),
                                    React.createElement(XAxis, { key: 'x', dataKey: 'date', tick: { fontSize: 12 } }),
                                    React.createElement(YAxis, { key: 'y', tick: { fontSize: 12 } }),
                                    React.createElement(Tooltip, { key: 'tooltip' }),
                                    React.createElement(Line, { key: 'line', type: 'monotone', dataKey: 'price', stroke: '#3B82F6', strokeWidth: 2 })
                                ]))
                            ]),

                            // Volume Chart
                            React.createElement('div', {
                                key: 'volume-chart',
                                style: {
                                    backgroundColor: 'white',
                                    borderRadius: '12px',
                                    padding: '24px',
                                    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
                                }
                            }, [
                                React.createElement('h3', {
                                    key: 'title',
                                    style: { fontSize: '20px', fontWeight: 'bold', marginBottom: '16px', display: 'flex', alignItems: 'center' }
                                }, [
                                    React.createElement(BarChart3, { key: 'icon', size: 20, color: '#10b981', style: { marginRight: '8px' } }),
                                    '成交量'
                                ]),
                                React.createElement(ResponsiveContainer, {
                                    key: 'chart',
                                    width: '100%',
                                    height: 250
                                }, React.createElement(BarChart, {
                                    data: generateHistoricalData().slice(-7)
                                }, [
                                    React.createElement(CartesianGrid, { key: 'grid', strokeDasharray: '3 3' }),
                                    React.createElement(XAxis, { key: 'x', dataKey: 'date', tick: { fontSize: 12 } }),
                                    React.createElement(YAxis, { key: 'y', tick: { fontSize: 12 } }),
                                    React.createElement(Tooltip, { key: 'tooltip' }),
                                    React.createElement(Bar, { key: 'bar', dataKey: 'volume', fill: '#10B981' })
                                ]))
                            ])
                        ]),

                        // AI Prediction
                        React.createElement('div', {
                            key: 'prediction',
                            style: {
                                backgroundColor: 'white',
                                borderRadius: '12px',
                                padding: '24px',
                                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
                            }
                        }, [
                            React.createElement('div', {
                                key: 'header',
                                style: { display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }
                            }, [
                                React.createElement('h3', {
                                    key: 'title',
                                    style: { fontSize: '20px', fontWeight: 'bold', display: 'flex', alignItems: 'center', margin: 0 }
                                }, [
                                    React.createElement(Brain, { key: 'icon', size: 20, color: '#8b5cf6', style: { marginRight: '8px' } }),
                                    'LSTM预测分析'
                                ]),
                                React.createElement('button', {
                                    key: 'button',
                                    onClick: runPrediction,
                                    disabled: isLoading,
                                    style: {
                                        backgroundColor: '#8b5cf6',
                                        color: 'white',
                                        padding: '8px 16px',
                                        borderRadius: '8px',
                                        border: 'none',
                                        cursor: isLoading ? 'not-allowed' : 'pointer',
                                        opacity: isLoading ? 0.5 : 1
                                    }
                                }, isLoading ? '分析中...' : '运行预测')
                            ]),
                            
                            isLoading ? React.createElement('div', {
                                key: 'loading',
                                style: { textAlign: 'center', padding: '32px' }
                            }, [
                                React.createElement('div', {
                                    key: 'spinner',
                                    style: { 
                                        width: '32px', 
                                        height: '32px', 
                                        border: '3px solid #f3f4f6', 
                                        borderTop: '3px solid #8b5cf6', 
                                        borderRadius: '50%', 
                                        animation: 'spin 1s linear infinite',
                                        margin: '0 auto 16px'
                                    }
                                }),
                                React.createElement('p', {
                                    key: 'text',
                                    style: { color: '#6b7280' }
                                }, '正在运行LSTM神经网络预测...')
                            ]) : null,
                            
                            predictions && !isLoading ? React.createElement('div', { key: 'results' }, [
                                React.createElement(ResponsiveContainer, {
                                    key: 'chart',
                                    width: '100%',
                                    height: 200
                                }, React.createElement(LineChart, {
                                    data: predictions
                                }, [
                                    React.createElement(CartesianGrid, { key: 'grid', strokeDasharray: '3 3' }),
                                    React.createElement(XAxis, { key: 'x', dataKey: 'day' }),
                                    React.createElement(YAxis, { key: 'y' }),
                                    React.createElement(Tooltip, { key: 'tooltip' }),
                                    React.createElement(Line, { key: 'line', type: 'monotone', dataKey: 'predictedPrice', stroke: '#8B5CF6', strokeWidth: 2 })
                                ])),
                                React.createElement('div', {
                                    key: 'grid',
                                    style: { 
                                        marginTop: '16px', 
                                        display: 'grid', 
                                        gridTemplateColumns: 'repeat(auto-fit, minmax(80px, 1fr))', 
                                        gap: '8px' 
                                    }
                                }, predictions.map((pred, idx) =>
                                    React.createElement('div', {
                                        key: idx,
                                        style: { 
                                            padding: '8px', 
                                            backgroundColor: '#f8fafc', 
                                            borderRadius: '8px', 
                                            textAlign: 'center' 
                                        }
                                    }, [
                                        React.createElement('div', { 
                                            key: 'day',
                                            style: { fontSize: '12px', color: '#6b7280' } 
                                        }, `第${pred.day}天`),
                                        React.createElement('div', { 
                                            key: 'price',
                                            style: { fontWeight: 'bold', fontSize: '14px' } 
                                        }, `$${pred.predictedPrice}`),
                                        React.createElement('div', { 
                                            key: 'confidence',
                                            style: { fontSize: '12px', color: '#059669' } 
                                        }, `${(pred.confidence * 100).toFixed(0)}%`)
                                    ])
                                ))
                            ]) : null
                        ])
                    ] : React.createElement('div', {
                        key: 'empty',
                        style: { textAlign: 'center', padding: '48px' }
                    }, [
                        React.createElement('div', {
                            key: 'icon',
                            style: { fontSize: '64px', marginBottom: '16px' }
                        }, '📈'),
                        React.createElement('h3', {
                            key: 'title',
                            style: { fontSize: '20px', fontWeight: '600', color: '#6b7280', marginBottom: '8px' }
                        }, '开始分析股票'),
                        React.createElement('p', {
                            key: 'subtitle',
                            style: { color: '#9ca3af' }
                        }, '在上方搜索框中输入股票代码开始分析')
                    ])
                ])
            ]);
        };

        ReactDOM.render(React.createElement(StockAnalysisTool), document.getElementById('root'));
    </script>
</body>
</html>
